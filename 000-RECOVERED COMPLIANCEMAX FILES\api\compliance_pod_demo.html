<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Compliance Pod Demo - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
        }
        
        .section-title {
            color: #4a5568;
            font-size: 1.8em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .section-title::before {
            content: '🎯';
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .disaster-selector {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #2d3748;
        }
        
        select, input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .category-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .category-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .category-card:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: scale(1.05);
        }
        
        .category-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .category-card h3 {
            font-size: 1.3em;
            margin-bottom: 8px;
        }
        
        .category-card p {
            font-size: 0.9em;
            opacity: 0.8;
        }
        
        .action-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .action-button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .two-prong-preview {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        
        .prong-card {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .prong-card h4 {
            color: #2d3748;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .prong-card ul {
            list-style: none;
            padding-left: 0;
        }
        
        .prong-card li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
            color: #4a5568;
        }
        
        .prong-card li::before {
            content: '✓';
            color: #48bb78;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .live-data-indicator {
            background: #48bb78;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            display: inline-block;
            margin-bottom: 20px;
        }
        
        .live-data-indicator::before {
            content: '●';
            margin-right: 5px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .api-status {
            background: #1a202c;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-top: 20px;
            font-size: 0.9em;
        }
        
        .loading {
            text-align: center;
            color: #667eea;
            font-style: italic;
        }
        
        @media (max-width: 768px) {
            .disaster-selector {
                grid-template-columns: 1fr;
            }
            
            .two-prong-preview {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Compliance Pod Demo</h1>
            <p>Two-Pronged Intake System with Live FEMA Data Integration</p>
            <div class="live-data-indicator">
                LIVE FEMA API DATA
            </div>
        </div>
        
        <div class="demo-card">
            <h2 class="section-title">Step 1: Select Active Disaster</h2>
            <div class="disaster-selector">
                <div class="form-group">
                    <label for="disaster-select">Active FEMA Disasters</label>
                    <select id="disaster-select">
                        <option value="">Loading live disasters...</option>
                        {% for disaster in disasters %}
                        <option value="{{ disaster.disaster_number }}" 
                                data-state="{{ disaster.state }}" 
                                data-type="{{ disaster.declaration_type }}"
                                data-programs="{{ disaster.available_programs|join(',') }}">
                            {{ disaster.disaster_number }} - {{ disaster.state }} ({{ disaster.declaration_type }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="state-filter">Filter by State</label>
                    <input type="text" id="state-filter" placeholder="Enter state name">
                </div>
                
                <div class="form-group">
                    <label>Available Programs</label>
                    <div id="programs-display" style="padding: 12px; background: #f7fafc; border-radius: 8px; min-height: 44px;">
                        Select a disaster to view available programs
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-card">
            <h2 class="section-title">Step 2: Select Compliance Category</h2>
            <div class="category-grid">
                <div class="category-card" data-category="A">
                    <h3>Category A</h3>
                    <p>Debris Removal</p>
                </div>
                <div class="category-card" data-category="B">
                    <h3>Category B</h3>
                    <p>Emergency Protective Measures</p>
                </div>
                <div class="category-card" data-category="C">
                    <h3>Category C</h3>
                    <p>Roads and Bridges</p>
                </div>
                <div class="category-card" data-category="D">
                    <h3>Category D</h3>
                    <p>Water Control Facilities</p>
                </div>
                <div class="category-card" data-category="E">
                    <h3>Category E</h3>
                    <p>Buildings and Equipment</p>
                </div>
                <div class="category-card" data-category="F">
                    <h3>Category F</h3>
                    <p>Utilities</p>
                </div>
                <div class="category-card" data-category="G">
                    <h3>Category G</h3>
                    <p>Parks and Recreation</p>
                </div>
            </div>
        </div>
        
        <div class="demo-card">
            <h2 class="section-title">Two-Pronged Compliance Preview</h2>
            <div class="two-prong-preview">
                <div class="prong-card">
                    <h4>🏢 PRONG 1: Applicant Requirements</h4>
                    <ul id="applicant-requirements">
                        <li>Select disaster and category to view requirements</li>
                    </ul>
                </div>
                <div class="prong-card">
                    <h4>⚙️ PRONG 2: System Processing</h4>
                    <ul id="system-requirements">
                        <li>Select disaster and category to view system actions</li>
                    </ul>
                </div>
            </div>
            
            <button class="action-button" id="generate-pod" disabled>
                Generate Compliance Pod
            </button>
            
            <div class="api-status" id="api-status" style="display: none;">
                Compliance pod generation status will appear here...
            </div>
        </div>
    </div>

    <script>
        // Compliance Pod Demo JavaScript
        let selectedDisaster = null;
        let selectedCategory = null;
        
        // Category requirements mapping
        const categoryRequirements = {
            'A': {
                applicant: [
                    'Debris removal documentation with before/after photos',
                    'Right of Entry (ROE) forms for all properties',
                    'Work order documentation and cost tracking',
                    'Environmental compliance certificates'
                ],
                system: [
                    'Validate ROE form completeness',
                    'Cross-check environmental requirements',
                    'Monitor 6-month completion timeline',
                    'Generate compliance reports'
                ]
            },
            'B': {
                applicant: [
                    'Life safety measure documentation',
                    'Emergency response activity reports',
                    'Cost documentation for protective measures',
                    'Coordination records with local emergency management'
                ],
                system: [
                    'Verify life safety compliance',
                    'Validate emergency response protocols',
                    'Track 60-day submission deadlines',
                    'Generate protective measure reports'
                ]
            },
            'C': {
                applicant: [
                    'Road and bridge damage assessments',
                    'Traffic impact analysis',
                    'Alternative route documentation',
                    'Engineering reports and design plans'
                ],
                system: [
                    'Validate engineering specifications',
                    'Cross-check traffic safety requirements',
                    'Monitor construction timelines',
                    'Generate infrastructure reports'
                ]
            }
        };
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadLiveDisasters();
            setupEventListeners();
        });
        
        function loadLiveDisasters() {
            fetch('/api/fema/disasters/live')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateDisasterSelect(data.data);
                    } else {
                        console.error('Failed to load disasters:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error loading disasters:', error);
                });
        }
        
        function updateDisasterSelect(disasters) {
            const select = document.getElementById('disaster-select');
            select.innerHTML = '<option value="">Select a disaster...</option>';
            
            disasters.forEach(disaster => {
                const option = document.createElement('option');
                option.value = disaster.disaster_number;
                option.textContent = `${disaster.disaster_number} - ${disaster.state} (${disaster.declaration_type})`;
                option.dataset.state = disaster.state;
                option.dataset.type = disaster.declaration_type;
                option.dataset.programs = disaster.available_programs.join(',');
                select.appendChild(option);
            });
        }
        
        function setupEventListeners() {
            // Disaster selection
            document.getElementById('disaster-select').addEventListener('change', function() {
                selectedDisaster = this.value;
                const option = this.selectedOptions[0];
                
                if (selectedDisaster) {
                    const programs = option.dataset.programs.split(',').filter(p => p);
                    document.getElementById('programs-display').innerHTML = 
                        programs.length > 0 ? programs.join(', ') : 'No programs listed';
                } else {
                    document.getElementById('programs-display').innerHTML = 
                        'Select a disaster to view available programs';
                }
                
                updateGenerateButton();
            });
            
            // Category selection
            document.querySelectorAll('.category-card').forEach(card => {
                card.addEventListener('click', function() {
                    // Remove previous selection
                    document.querySelectorAll('.category-card').forEach(c => 
                        c.classList.remove('selected'));
                    
                    // Add selection to clicked card
                    this.classList.add('selected');
                    selectedCategory = this.dataset.category;
                    
                    updateRequirementsPreview();
                    updateGenerateButton();
                });
            });
            
            // Generate pod button
            document.getElementById('generate-pod').addEventListener('click', generateCompliancePod);
            
            // State filter
            document.getElementById('state-filter').addEventListener('input', function() {
                const filter = this.value.toLowerCase();
                const select = document.getElementById('disaster-select');
                
                Array.from(select.options).forEach(option => {
                    if (option.value) {
                        const state = option.dataset.state.toLowerCase();
                        option.style.display = state.includes(filter) ? 'block' : 'none';
                    }
                });
            });
        }
        
        function updateRequirementsPreview() {
            if (!selectedCategory) return;
            
            const requirements = categoryRequirements[selectedCategory] || {
                applicant: ['Category-specific requirements will be loaded'],
                system: ['System processing steps will be defined']
            };
            
            // Update applicant requirements
            const applicantList = document.getElementById('applicant-requirements');
            applicantList.innerHTML = '';
            requirements.applicant.forEach(req => {
                const li = document.createElement('li');
                li.textContent = req;
                applicantList.appendChild(li);
            });
            
            // Update system requirements
            const systemList = document.getElementById('system-requirements');
            systemList.innerHTML = '';
            requirements.system.forEach(req => {
                const li = document.createElement('li');
                li.textContent = req;
                systemList.appendChild(li);
            });
        }
        
        function updateGenerateButton() {
            const button = document.getElementById('generate-pod');
            button.disabled = !(selectedDisaster && selectedCategory);
            
            if (selectedDisaster && selectedCategory) {
                button.textContent = `Generate ${selectedCategory} Pod for ${selectedDisaster}`;
            } else {
                button.textContent = 'Generate Compliance Pod';
            }
        }
        
        function generateCompliancePod() {
            if (!selectedDisaster || !selectedCategory) return;
            
            const statusDiv = document.getElementById('api-status');
            statusDiv.style.display = 'block';
            statusDiv.innerHTML = 'Generating compliance pod...';
            
            fetch(`/api/fema/compliance-pod/${selectedDisaster}/${selectedCategory}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusDiv.innerHTML = `
                            <strong>✅ Compliance Pod Generated Successfully!</strong><br>
                            Disaster: ${data.disaster_number}<br>
                            Category: ${data.category}<br>
                            Generated: ${new Date(data.generated_at).toLocaleString()}<br>
                            <br>
                            <a href="/compliance-pod-interface/${data.disaster_number}/${data.category}" 
                               style="color: #68d391; text-decoration: underline;">
                               → Open Interactive Compliance Pod Interface
                            </a>
                        `;
                    } else {
                        statusDiv.innerHTML = `<strong>❌ Error:</strong> ${data.error}`;
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `<strong>❌ Network Error:</strong> ${error.message}`;
                });
        }
    </script>
</body>
</html> 