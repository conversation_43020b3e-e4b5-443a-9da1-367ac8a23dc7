# ComplianceMax Environment Configuration
# Copy this file to .env and update with your actual values

# =================================
# DATABASE CONFIGURATION
# =================================
DATABASE_URL=postgresql://compliancemax_user:compliancemax_secure_2025@localhost:5432/compliancemax
DB_MAX_CONNECTIONS=20
DB_SSL=false

# =================================
# REDIS CONFIGURATION
# =================================
REDIS_URL=redis://localhost:6379
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000

# =================================
# APPLICATION CONFIGURATION
# =================================
NODE_ENV=development
PORT=3333
HOSTNAME=0.0.0.0
LOG_LEVEL=info

# =================================
# SECURITY CONFIGURATION
# =================================
NEXTAUTH_SECRET=compliancemax-jwt-secret-2025-change-in-production
NEXTAUTH_URL=http://localhost:3333
SESSION_SECRET=compliancemax-session-secret-2025
CORS_ORIGIN=http://localhost:3333,http://localhost:3000

# =================================
# RATE LIMITING
# =================================
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# =================================
# FEMA API INTEGRATION
# =================================
FEMA_API_URL=https://www.fema.gov/api
FEMA_API_KEY=your-fema-api-key-here
FEMA_RETRY_ATTEMPTS=3
FEMA_TIMEOUT_MS=30000

# =================================
# DOCUMENT PROCESSING (DOCLING)
# =================================
DOCLING_OCR_ENABLED=true
DOCLING_MAX_FILE_SIZE=50000000
DOCLING_ALLOWED_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,image/jpeg,image/png,text/plain
DOCLING_TIMEOUT=300000

# =================================
# PRISMA CONFIGURATION
# =================================
PRISMA_CLI_BINARY_TARGETS=native
PRISMA_QUERY_ENGINE_BINARY=native

# =================================
# DEVELOPMENT OVERRIDES
# =================================
# Uncomment for development
# DEBUG=true
# VERBOSE_LOGGING=true
# MOCK_EXTERNAL_APIS=false

# =================================
# PRODUCTION SETTINGS
# =================================
# For production deployment, update these:
# NODE_ENV=production
# DB_SSL=true
# LOG_LEVEL=warn
# CORS_ORIGIN=https://yourdomain.com 