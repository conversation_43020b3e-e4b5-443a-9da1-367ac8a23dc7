"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedEditPanel = void 0;
const vscode = __importStar(require("vscode"));
class AdvancedEditPanel {
    constructor(panel, fileProcessor, logger) {
        this.panel = panel;
        this.fileProcessor = fileProcessor;
        this.logger = logger;
        this.panel.onDidDispose(() => this.dispose());
        this.panel.webview.onDidReceiveMessage(message => this.handleMessage(message), undefined);
    }
    initialize(filePath) {
        this.panel.webview.html = this.getWebviewContent(filePath);
    }
    async handleMessage(message) {
        switch (message.command) {
            case 'executeEdit':
                await this.executeEdit(message.data);
                break;
            case 'previewEdit':
                await this.previewEdit(message.data);
                break;
            case 'getFileContent':
                await this.getFileContent(message.filePath);
                break;
        }
    }
    async executeEdit(data) {
        try {
            const command = {
                type: data.type,
                instructions: data.instructions,
                filePath: data.filePath,
                lineNumber: data.lineNumber,
                startLine: data.startLine,
                endLine: data.endLine,
                content: data.content,
                searchPattern: data.searchPattern,
                replaceWith: data.replaceWith
            };
            const result = await this.fileProcessor.processEdit(command);
            this.panel.webview.postMessage({
                command: 'editResult',
                result: result
            });
        }
        catch (error) {
            this.logger.error('Advanced edit execution failed', error);
            this.panel.webview.postMessage({
                command: 'editResult',
                result: {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                }
            });
        }
    }
    async previewEdit(data) {
        // Implementation for edit preview would go here
        // For now, just return a simple preview
        this.panel.webview.postMessage({
            command: 'previewResult',
            preview: `Preview for: ${data.instructions}`
        });
    }
    async getFileContent(filePath) {
        try {
            const document = await vscode.workspace.openTextDocument(filePath);
            this.panel.webview.postMessage({
                command: 'fileContent',
                content: document.getText(),
                lineCount: document.lineCount
            });
        }
        catch (error) {
            this.logger.error('Failed to get file content', error);
        }
    }
    getWebviewContent(filePath) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Edit Tool - Advanced</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            background-color: var(--vscode-panel-background);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            font-family: inherit;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .secondary-button {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }
        .secondary-button:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 3px;
            display: none;
        }
        .result.success {
            background-color: var(--vscode-testing-iconPassed);
            color: var(--vscode-testing-message-info-foreground);
        }
        .result.error {
            background-color: var(--vscode-testing-iconFailed);
            color: var(--vscode-testing-message-error-foreground);
        }
        .file-info {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 20px;
        }
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--vscode-panel-border);
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: var(--vscode-focusBorder);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced Edit Tool - Advanced Editor</h1>
        
        <div class="file-info">
            <strong>File:</strong> <span id="filePath">${filePath}</span><br>
            <strong>Lines:</strong> <span id="lineCount">Loading...</span>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('simple')">Simple Edit</div>
            <div class="tab" onclick="switchTab('advanced')">Advanced Edit</div>
            <div class="tab" onclick="switchTab('batch')">Batch Operations</div>
        </div>

        <div id="simple" class="tab-content active">
            <div class="section">
                <h3>Simple Edit Operations</h3>
                <div class="form-group">
                    <label for="simpleInstructions">Edit Instructions:</label>
                    <textarea id="simpleInstructions" placeholder="Enter natural language instructions (e.g., 'replace line 10 with new content')"></textarea>
                </div>
                <button onclick="executeSimpleEdit()">Execute Edit</button>
                <button class="secondary-button" onclick="previewSimpleEdit()">Preview</button>
            </div>
        </div>

        <div id="advanced" class="tab-content">
            <div class="section">
                <h3>Advanced Edit Operations</h3>
                <div class="form-group">
                    <label for="operationType">Operation Type:</label>
                    <select id="operationType" onchange="updateAdvancedForm()">
                        <option value="replace">Replace</option>
                        <option value="insert">Insert</option>
                        <option value="delete">Delete</option>
                        <option value="search_replace">Search & Replace</option>
                    </select>
                </div>
                
                <div id="lineNumberGroup" class="form-group">
                    <label for="lineNumber">Line Number:</label>
                    <input type="number" id="lineNumber" min="1">
                </div>
                
                <div id="rangeGroup" class="form-group" style="display: none;">
                    <label for="startLine">Start Line:</label>
                    <input type="number" id="startLine" min="1">
                    <label for="endLine">End Line:</label>
                    <input type="number" id="endLine" min="1">
                </div>
                
                <div id="contentGroup" class="form-group">
                    <label for="content">Content:</label>
                    <textarea id="content" placeholder="Enter the new content"></textarea>
                </div>
                
                <div id="searchReplaceGroup" style="display: none;">
                    <div class="form-group">
                        <label for="searchPattern">Search Pattern:</label>
                        <input type="text" id="searchPattern" placeholder="Text or regex to search for">
                    </div>
                    <div class="form-group">
                        <label for="replaceWith">Replace With:</label>
                        <input type="text" id="replaceWith" placeholder="Replacement text">
                    </div>
                </div>
                
                <button onclick="executeAdvancedEdit()">Execute Edit</button>
                <button class="secondary-button" onclick="previewAdvancedEdit()">Preview</button>
            </div>
        </div>

        <div id="batch" class="tab-content">
            <div class="section">
                <h3>Batch Operations</h3>
                <div class="form-group">
                    <label for="batchInstructions">Batch Instructions (one per line):</label>
                    <textarea id="batchInstructions" placeholder="Enter multiple edit instructions, one per line"></textarea>
                </div>
                <button onclick="executeBatchEdit()">Execute Batch</button>
                <button class="secondary-button" onclick="previewBatchEdit()">Preview Batch</button>
            </div>
        </div>

        <div id="result" class="result"></div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentFilePath = '${filePath}';

        // Initialize
        vscode.postMessage({
            command: 'getFileContent',
            filePath: currentFilePath
        });

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'fileContent':
                    document.getElementById('lineCount').textContent = message.lineCount;
                    break;
                case 'editResult':
                    showResult(message.result);
                    break;
                case 'previewResult':
                    showPreview(message.preview);
                    break;
            }
        });

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function updateAdvancedForm() {
            const operationType = document.getElementById('operationType').value;
            const lineNumberGroup = document.getElementById('lineNumberGroup');
            const rangeGroup = document.getElementById('rangeGroup');
            const contentGroup = document.getElementById('contentGroup');
            const searchReplaceGroup = document.getElementById('searchReplaceGroup');

            // Reset visibility
            lineNumberGroup.style.display = 'block';
            rangeGroup.style.display = 'none';
            contentGroup.style.display = 'block';
            searchReplaceGroup.style.display = 'none';

            if (operationType === 'search_replace') {
                lineNumberGroup.style.display = 'none';
                contentGroup.style.display = 'none';
                searchReplaceGroup.style.display = 'block';
            } else if (operationType === 'delete') {
                contentGroup.style.display = 'none';
            }
        }

        function executeSimpleEdit() {
            const instructions = document.getElementById('simpleInstructions').value;
            if (!instructions.trim()) {
                showResult({ success: false, error: 'Please enter edit instructions' });
                return;
            }

            vscode.postMessage({
                command: 'executeEdit',
                data: {
                    type: 'modify',
                    instructions: instructions,
                    filePath: currentFilePath
                }
            });
        }

        function previewSimpleEdit() {
            const instructions = document.getElementById('simpleInstructions').value;
            vscode.postMessage({
                command: 'previewEdit',
                data: {
                    instructions: instructions,
                    filePath: currentFilePath
                }
            });
        }

        function executeAdvancedEdit() {
            const operationType = document.getElementById('operationType').value;
            const data = {
                type: operationType,
                filePath: currentFilePath
            };

            if (operationType === 'search_replace') {
                data.searchPattern = document.getElementById('searchPattern').value;
                data.replaceWith = document.getElementById('replaceWith').value;
                data.instructions = \`replace "\${data.searchPattern}" with "\${data.replaceWith}"\`;
            } else {
                data.lineNumber = parseInt(document.getElementById('lineNumber').value);
                if (operationType !== 'delete') {
                    data.content = document.getElementById('content').value;
                }
                data.instructions = \`\${operationType} line \${data.lineNumber}\${data.content ? ' with ' + data.content : ''}\`;
            }

            vscode.postMessage({
                command: 'executeEdit',
                data: data
            });
        }

        function previewAdvancedEdit() {
            // Similar to executeAdvancedEdit but for preview
            const operationType = document.getElementById('operationType').value;
            const data = {
                type: operationType,
                filePath: currentFilePath
            };

            vscode.postMessage({
                command: 'previewEdit',
                data: data
            });
        }

        function executeBatchEdit() {
            const instructions = document.getElementById('batchInstructions').value;
            if (!instructions.trim()) {
                showResult({ success: false, error: 'Please enter batch instructions' });
                return;
            }

            vscode.postMessage({
                command: 'executeEdit',
                data: {
                    type: 'modify',
                    instructions: instructions,
                    filePath: currentFilePath
                }
            });
        }

        function previewBatchEdit() {
            const instructions = document.getElementById('batchInstructions').value;
            vscode.postMessage({
                command: 'previewEdit',
                data: {
                    instructions: instructions,
                    filePath: currentFilePath
                }
            });
        }

        function showResult(result) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            
            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = \`
                    <strong>Success!</strong><br>
                    Changes applied: \${result.changesApplied}<br>
                    Processing time: \${result.processingTime}ms
                    \${result.backupPath ? '<br>Backup created: ' + result.backupPath : ''}
                \`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = \`<strong>Error:</strong> \${result.error}\`;
            }
        }

        function showPreview(preview) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.innerHTML = \`<strong>Preview:</strong><br>\${preview}\`;
        }
    </script>
</body>
</html>`;
    }
    dispose() {
        this.panel.dispose();
    }
}
exports.AdvancedEditPanel = AdvancedEditPanel;
//# sourceMappingURL=advancedEditPanel.js.map