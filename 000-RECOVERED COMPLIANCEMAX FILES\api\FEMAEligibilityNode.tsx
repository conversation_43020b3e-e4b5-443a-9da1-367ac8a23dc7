'use client';

import React, { memo, useState } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { motion } from 'framer-motion';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  ClockIcon,
  DocumentCheckIcon,
  CurrencyDollarIcon
} from 'lucide-react';
import { WorkflowNode, FEMACategory, FEMAProgram } from '../../types/workflow';

interface FEMAEligibilityNodeData {
  label: string;
  description?: string;
  femaConfig?: {
    program: FEMAProgram;
    category?: FEMACategory;
    eligibilityRules?: Array<{
      id: string;
      name: string;
      condition: string;
      enabled: boolean;
    }>;
    requiredDocuments?: Array<{
      id: string;
      name: string;
      required: boolean;
    }>;
  };
  status?: 'pending' | 'checking' | 'eligible' | 'ineligible' | 'needs-review';
  lastChecked?: Date;
  eligibilityScore?: number;
  issues?: Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
  }>;
}

interface FEMAEligibilityNodeProps extends NodeProps<FEMAEligibilityNodeData> {
  selected?: boolean;
}

const FEMAEligibilityNode: React.FC<FEMAEligibilityNodeProps> = ({ 
  data, 
  selected,
  id 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const {
    label = 'FEMA Eligibility Check',
    description,
    femaConfig,
    status = 'pending',
    lastChecked,
    eligibilityScore,
    issues = []
  } = data;

  const getStatusColor = () => {
    switch (status) {
      case 'eligible': return 'text-green-600 bg-green-50 border-green-200';
      case 'ineligible': return 'text-red-600 bg-red-50 border-red-200';
      case 'checking': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'needs-review': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'eligible': return <CheckCircleIcon className="w-4 h-4" />;
      case 'ineligible': return <ExclamationTriangleIcon className="w-4 h-4" />;
      case 'checking': return <ClockIcon className="w-4 h-4 animate-spin" />;
      case 'needs-review': return <DocumentCheckIcon className="w-4 h-4" />;
      default: return <ClockIcon className="w-4 h-4" />;
    }
  };

  const getFEMACategoryDisplay = (category?: FEMACategory) => {
    if (!category) return null;
    const categoryMap = {
      'CATEGORY_A': 'Cat A - Debris Removal',
      'CATEGORY_B': 'Cat B - Emergency Protective',
      'CATEGORY_C': 'Cat C - Roads & Bridges',
      'CATEGORY_D': 'Cat D - Water Control',
      'CATEGORY_E': 'Cat E - Buildings & Equipment',
      'CATEGORY_F': 'Cat F - Utilities',
      'CATEGORY_G': 'Cat G - Parks & Recreation',
      'CATEGORY_Z': 'Cat Z - State Management'
    };
    return categoryMap[category] || category;
  };

  const getProgramDisplay = (program?: FEMAProgram) => {
    if (!program) return null;
    const programMap = {
      'PUBLIC_ASSISTANCE': 'Public Assistance',
      'HAZARD_MITIGATION': 'Hazard Mitigation',
      'INDIVIDUAL_ASSISTANCE': 'Individual Assistance',
      'DISASTER_RELIEF_FUND': 'Disaster Relief Fund',
      'FIRE_MANAGEMENT': 'Fire Management'
    };
    return programMap[program] || program;
  };

  return (
    <>
      {/* Input Handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-blue-500 border-2 border-white"
      />

      <motion.div
        className={`
          bg-white rounded-lg shadow-lg border-2 min-w-64 max-w-80
          ${selected ? 'border-blue-500 shadow-blue-200' : 'border-gray-200'}
          ${getStatusColor()}
        `}
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        {/* Header */}
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <div className="flex-shrink-0">
              {getStatusIcon()}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-gray-900 truncate">
                {label}
              </h3>
              {femaConfig?.program && (
                <p className="text-xs text-gray-500">
                  {getProgramDisplay(femaConfig.program)}
                </p>
              )}
            </div>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Status Bar */}
        <div className="px-4 py-2 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between text-xs">
            <span className="font-medium">
              Status: <span className="capitalize">{status.replace('-', ' ')}</span>
            </span>
            {eligibilityScore !== undefined && (
              <span className="flex items-center gap-1">
                <CurrencyDollarIcon className="w-3 h-3" />
                Score: {eligibilityScore}%
              </span>
            )}
          </div>
          
          {femaConfig?.category && (
            <div className="mt-1">
              <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                {getFEMACategoryDisplay(femaConfig.category)}
              </span>
            </div>
          )}
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            transition={{ duration: 0.3 }}
            className="px-4 py-3 space-y-3"
          >
            {/* Description */}
            {description && (
              <div>
                <p className="text-xs text-gray-600">{description}</p>
              </div>
            )}

            {/* Eligibility Rules */}
            {femaConfig?.eligibilityRules && femaConfig.eligibilityRules.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-gray-700 mb-2">Eligibility Rules</h4>
                <div className="space-y-1">
                  {femaConfig.eligibilityRules.slice(0, 3).map((rule) => (
                    <div 
                      key={rule.id}
                      className={`flex items-center gap-2 text-xs p-2 rounded ${
                        rule.enabled ? 'bg-green-50 text-green-700' : 'bg-gray-50 text-gray-500'
                      }`}
                    >
                      <div className={`w-2 h-2 rounded-full ${
                        rule.enabled ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                      <span className="truncate">{rule.name}</span>
                    </div>
                  ))}
                  {femaConfig.eligibilityRules.length > 3 && (
                    <p className="text-xs text-gray-500 text-center">
                      +{femaConfig.eligibilityRules.length - 3} more rules
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Required Documents */}
            {femaConfig?.requiredDocuments && femaConfig.requiredDocuments.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-gray-700 mb-2">Required Documents</h4>
                <div className="space-y-1">
                  {femaConfig.requiredDocuments.slice(0, 3).map((doc) => (
                    <div 
                      key={doc.id}
                      className="flex items-center gap-2 text-xs p-2 bg-gray-50 rounded"
                    >
                      <DocumentCheckIcon className="w-3 h-3 text-gray-400" />
                      <span className="truncate">{doc.name}</span>
                      {doc.required && (
                        <span className="text-red-500">*</span>
                      )}
                    </div>
                  ))}
                  {femaConfig.requiredDocuments.length > 3 && (
                    <p className="text-xs text-gray-500 text-center">
                      +{femaConfig.requiredDocuments.length - 3} more documents
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Issues */}
            {issues.length > 0 && (
              <div>
                <h4 className="text-xs font-medium text-gray-700 mb-2">Issues</h4>
                <div className="space-y-1">
                  {issues.slice(0, 2).map((issue, index) => (
                    <div 
                      key={index}
                      className={`text-xs p-2 rounded ${
                        issue.type === 'error' ? 'bg-red-50 text-red-700' :
                        issue.type === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                        'bg-blue-50 text-blue-700'
                      }`}
                    >
                      {issue.message}
                    </div>
                  ))}
                  {issues.length > 2 && (
                    <p className="text-xs text-gray-500 text-center">
                      +{issues.length - 2} more issues
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Last Checked */}
            {lastChecked && (
              <div className="text-xs text-gray-500 text-center border-t pt-2">
                Last checked: {lastChecked.toLocaleString()}
              </div>
            )}
          </motion.div>
        )}

        {/* Footer Actions */}
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 rounded-b-lg">
          <div className="flex items-center justify-between">
            <button className="text-xs text-blue-600 hover:text-blue-800 font-medium">
              Run Check
            </button>
            <div className="flex gap-2">
              <button className="text-xs text-gray-500 hover:text-gray-700">
                View Details
              </button>
              <button className="text-xs text-gray-500 hover:text-gray-700">
                Configure
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Output Handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="eligible"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="ineligible"
        style={{ top: '50%' }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="needs-review"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-yellow-500 border-2 border-white"
      />
    </>
  );
};

export default memo(FEMAEligibilityNode); 