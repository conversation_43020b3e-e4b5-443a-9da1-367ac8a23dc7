<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .card {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 10px;
            min-height: 20px;
        }
    </style>
</head>
<body>
    <h1>ComplianceMax API Test</h1>
    
    <div class="card">
        <h2>Standalone Backend (Port 8000)</h2>
        <button onclick="testEndpoint('http://localhost:8000/api/v1/health', 'standalone-result')">Test Health Endpoint</button>
        <button onclick="testEndpoint('http://localhost:8000/', 'standalone-result')">Test Root Endpoint</button>
        <div id="standalone-result" class="result">
            <pre>Results will appear here...</pre>
        </div>
    </div>

    <div class="card">
        <h2>Main Backend (Port 8001)</h2>
        <button onclick="testEndpoint('http://localhost:8001/api/v1/health', 'main-result')">Test Health Endpoint</button>
        <button onclick="testEndpoint('http://localhost:8001/', 'main-result')">Test Root Endpoint</button>
        <div id="main-result" class="result">
            <pre>Results will appear here...</pre>
        </div>
    </div>

    <div class="card">
        <h2>Frontend (Port 3000)</h2>
        <button onclick="window.open('http://localhost:3000', '_blank')">Open Frontend</button>
        <p>This will open the frontend in a new tab.</p>
    </div>

    <script>
        async function testEndpoint(url, resultId) {
            const resultElement = document.getElementById(resultId);
            resultElement.innerHTML = '<pre>Testing connection to ' + url + '...</pre>';
            
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                resultElement.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                resultElement.innerHTML = '<pre>Error: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html> 