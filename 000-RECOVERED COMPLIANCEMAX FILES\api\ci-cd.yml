name: ComplianceMax CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 2 * * 0'  # Weekly security scan

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # Security and Dependency Scanning
  security-scan:
    name: Security & Dependency Audit
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json
      
      - name: Install dependencies
        working-directory: ./app
        run: npm ci
      
      - name: Run npm audit
        working-directory: ./app
        run: npm audit --audit-level=moderate
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=medium --file=app/package.json

  # Database Schema Testing
  database-tests:
    name: Database Schema & Migration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: compliancemax_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Test database schema
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/compliancemax_test
        run: |
          psql $DATABASE_URL -f SRC/database/schema.sql
          psql $DATABASE_URL -c "SELECT table_name FROM information_schema.tables WHERE table_schema='public';"

  # Application Testing
  test:
    name: Application Tests
    runs-on: ubuntu-latest
    needs: [security-scan, database-tests]
    
    strategy:
      matrix:
        test-type: [unit, integration, api]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: compliancemax_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json
      
      - name: Install dependencies
        working-directory: ./app
        run: npm ci
      
      - name: Setup test database
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/compliancemax_test
        run: |
          psql $DATABASE_URL -f SRC/database/schema.sql
      
      - name: Generate Prisma client
        working-directory: ./app
        run: npx prisma generate
      
      - name: Run linting
        working-directory: ./app
        run: npm run lint
      
      - name: Run ${{ matrix.test-type }} tests
        working-directory: ./app
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/compliancemax_test
          REDIS_URL: redis://localhost:6379
          NODE_ENV: test
        run: |
          case "${{ matrix.test-type }}" in
            "unit")
              npm run test:app
              ;;
            "integration") 
              npm run test:import
              ;;
            "api")
              npm run test:full
              ;;
          esac

  # Build and Package
  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [test]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json
      
      - name: Install dependencies
        working-directory: ./app
        run: npm ci
      
      - name: Generate Prisma client
        working-directory: ./app
        run: npx prisma generate
      
      - name: Build Next.js application
        working-directory: ./app
        run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: |
            app/.next/
            app/public/
          retention-days: 7

  # Docker Build and Registry Push
  docker-build:
    name: Build & Push Docker Images
    runs-on: ubuntu-latest
    needs: [build]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: ./app
          file: ./app/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Performance Testing
  performance-test:
    name: Performance & Load Testing
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Start services with Docker Compose
        run: |
          docker-compose up -d
          sleep 30  # Wait for services to be ready
      
      - name: Wait for application health
        run: |
          timeout 300 bash -c 'until curl -f http://localhost:3333/api/health; do sleep 5; done'
      
      - name: Run performance tests
        run: |
          # Basic performance test - can be expanded with k6 or Artillery
          curl -w "@perf-format.txt" -s -o /dev/null http://localhost:3333/api/health
          curl -w "@perf-format.txt" -s -o /dev/null http://localhost:3333/api/projects
      
      - name: Cleanup
        if: always()
        run: docker-compose down -v

  # Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [performance-test]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Deploy to staging environment
        run: |
          echo "Deploying to staging..."
          # Add staging deployment logic here

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [performance-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Deploy to production environment
        run: |
          echo "Deploying to production..."
          # Add production deployment logic here
      
      - name: Create deployment notification
        if: success()
        run: |
          echo "✅ ComplianceMax V74 deployed successfully to production"
          # Add Slack/Teams notification here 