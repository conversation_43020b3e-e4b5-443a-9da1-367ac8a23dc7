# ComplianceMax System: FEMA Compliance Optimization Implementation Plan

## Executive Summary

This document outlines a comprehensive implementation plan for optimizing the ComplianceMax system's compliance capabilities based on FEMA Public Assistance (PA) policies. The plan addresses five key areas:

1. Analysis of the current compliance validation engine
2. Enhancement of the validation engine with comprehensive FEMA policy checks
3. Improvement of reporting capabilities
4. Implementation of audit readiness features
5. Creation of a policy update mechanism

Each section includes detailed recommendations, implementation strategies, and code examples to guide the development team in implementing these optimizations.

## 1. Analysis of Current Compliance Validation Engine

### 1.1 Current State Assessment

Based on our analysis, the ComplianceMax system's current compliance validation engine likely has the following limitations:

- **Hard-coded validation rules**: Rules are embedded directly in the codebase, making updates difficult when FEMA policies change
- **Limited rule coverage**: Only covers a subset of FEMA PA requirements
- **Manual validation processes**: Relies heavily on human review for complex compliance checks
- **Siloed validation**: Compliance checks operate independently without a unified framework
- **Limited traceability**: Difficult to trace validation results back to specific FEMA policies
- **Static validation**: Rules don't adapt to different disaster declarations or policy versions

### 1.2 Gap Analysis

| Compliance Area | Current State | Gap | Recommendation |
|-----------------|---------------|-----|----------------|
| FEMA Policy Coverage | Limited to core requirements | Missing recent policy updates (PAPPG v5.0) | Implement comprehensive policy registry |
| Validation Flexibility | Hard-coded rules | Cannot adapt to policy changes | Create configurable rule engine |
| Documentation Linkage | Manual cross-referencing | No direct links between findings and policy citations | Implement policy citation system |
| Audit Trail | Basic logging | Insufficient for FEMA audits | Enhance with immutable audit logging |
| Policy Updates | Manual updates | Delays in implementing new policies | Create automated policy update mechanism |

## 2. Enhanced Validation Engine with Comprehensive FEMA Policy Checks

### 2.1 Modular Rule Registry Architecture

The enhanced validation engine should be built on a modular architecture that separates policy definitions from validation logic:

```python
# validation_engine/models.py
from enum import Enum
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime
import re

class RuleType(Enum):
    REGEX = "regex"
    DATE = "date"
    NUMERIC = "numeric"
    BOOLEAN = "boolean"
    CUSTOM = "custom"

class Severity(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class PolicySource(Enum):
    PAPPG_V5 = "PAPPG v5.0"
    CFR_44_206 = "44 CFR 206"
    STAFFORD_ACT = "Stafford Act"
    DRRA = "DRRA"
    CUSTOM = "Custom Policy"

class ValidationRule:
    def __init__(
        self,
        rule_id: str,
        name: str,
        description: str,
        rule_type: RuleType,
        policy_source: PolicySource,
        policy_citation: str,
        severity: Severity,
        pattern: Optional[str] = None,
        min_value: Optional[float] = None,
        max_value: Optional[float] = None,
        custom_validator: Optional[Callable] = None,
        effective_date: datetime = None,
        expiration_date: Optional[datetime] = None,
        applies_to_categories: List[str] = None,
        metadata: Dict[str, Any] = None
    ):
        self.rule_id = rule_id
        self.name = name
        self.description = description
        self.rule_type = rule_type
        self.policy_source = policy_source
        self.policy_citation = policy_citation
        self.severity = severity
        self.pattern = pattern
        self.min_value = min_value
        self.max_value = max_value
        self.custom_validator = custom_validator
        self.effective_date = effective_date or datetime.now()
        self.expiration_date = expiration_date
        self.applies_to_categories = applies_to_categories or []
        self.metadata = metadata or {}
        
    def validate(self, value: Any, context: Dict[str, Any] = None) -> bool:
        """Validate a value against this rule"""
        if self.rule_type == RuleType.REGEX:
            return bool(re.match(self.pattern, str(value)))
        elif self.rule_type == RuleType.NUMERIC:
            try:
                num_value = float(value)
                min_check = True if self.min_value is None else num_value >= self.min_value
                max_check = True if self.max_value is None else num_value <= self.max_value
                return min_check and max_check
            except (ValueError, TypeError):
                return False
        elif self.rule_type == RuleType.DATE:
            # Date validation logic
            return True  # Placeholder
        elif self.rule_type == RuleType.BOOLEAN:
            return bool(value)
        elif self.rule_type == RuleType.CUSTOM and self.custom_validator:
            return self.custom_validator(value, context)
        return False
```

### 2.2 Rule Registry Implementation

Create a YAML-based rule registry that can be easily updated when FEMA policies change:

```yaml
# validation_engine/rules/fema_pa_rules.yaml
rules:
  - rule_id: "PA-PROC-001"
    name: "Procurement Documentation"
    description: "Verify procurement documentation meets federal requirements"
    rule_type: "custom"
    policy_source: "PAPPG_V5"
    policy_citation: "PAPPG v5.0, Chapter 2, Section IV.V"
    severity: "critical"
    applies_to_categories: ["procurement", "contracts"]
    metadata:
      validator_function: "validate_procurement_documentation"
      required_documents: ["contract", "bid_documentation", "cost_analysis"]
      
  - rule_id: "PA-INS-001"
    name: "Insurance Requirements"
    description: "Verify insurance requirements are met for facilities in SFHA"
    rule_type: "custom"
    policy_source: "44_CFR_206"
    policy_citation: "44 CFR 206.252(d)"
    severity: "critical"
    applies_to_categories: ["insurance", "facilities"]
    metadata:
      validator_function: "validate_insurance_requirements"
      
  - rule_id: "PA-DOC-001"
    name: "Documentation Retention"
    description: "Verify documentation retention period compliance"
    rule_type: "numeric"
    policy_source: "2_CFR_200"
    policy_citation: "2 CFR 200.334"
    severity: "warning"
    min_value: 3
    applies_to_categories: ["documentation", "records"]
    metadata:
      unit: "years"
      description: "Records must be retained for at least 3 years from final expenditure report"
```

### 2.3 Rule Loader Implementation

```python
# validation_engine/rule_loader.py
import yaml
import os
import logging
from datetime import datetime
from typing import List, Dict, Any

from .models import ValidationRule, RuleType, Severity, PolicySource

logger = logging.getLogger(__name__)

class RuleRegistry:
    def __init__(self):
        self.rules = {}
        self.rule_validators = {}
        
    def register_validator(self, validator_name, validator_function):
        """Register a custom validator function"""
        self.rule_validators[validator_name] = validator_function
        
    def load_rules_from_yaml(self, yaml_file_path):
        """Load validation rules from a YAML file"""
        if not os.path.exists(yaml_file_path):
            logger.error(f"Rule file not found: {yaml_file_path}")
            return
            
        try:
            with open(yaml_file_path, 'r') as file:
                rule_data = yaml.safe_load(file)
                
            for rule_dict in rule_data.get('rules', []):
                rule_id = rule_dict.get('rule_id')
                if not rule_id:
                    logger.warning("Rule without ID found, skipping")
                    continue
                    
                # Convert string enums to enum values
                rule_type = RuleType(rule_dict.get('rule_type', 'custom'))
                severity = Severity(rule_dict.get('severity', 'warning'))
                policy_source = PolicySource(rule_dict.get('policy_source', 'CUSTOM'))
                
                # Handle dates
                effective_date = rule_dict.get('effective_date')
                if effective_date and isinstance(effective_date, str):
                    effective_date = datetime.fromisoformat(effective_date)
                    
                expiration_date = rule_dict.get('expiration_date')
                if expiration_date and isinstance(expiration_date, str):
                    expiration_date = datetime.fromisoformat(expiration_date)
                
                # Handle custom validator
                custom_validator = None
                if rule_type == RuleType.CUSTOM:
                    validator_name = rule_dict.get('metadata', {}).get('validator_function')
                    if validator_name and validator_name in self.rule_validators:
                        custom_validator = self.rule_validators[validator_name]
                
                # Create rule object
                rule = ValidationRule(
                    rule_id=rule_id,
                    name=rule_dict.get('name', ''),
                    description=rule_dict.get('description', ''),
                    rule_type=rule_type,
                    policy_source=policy_source,
                    policy_citation=rule_dict.get('policy_citation', ''),
                    severity=severity,
                    pattern=rule_dict.get('pattern'),
                    min_value=rule_dict.get('min_value'),
                    max_value=rule_dict.get('max_value'),
                    custom_validator=custom_validator,
                    effective_date=effective_date,
                    expiration_date=expiration_date,
                    applies_to_categories=rule_dict.get('applies_to_categories', []),
                    metadata=rule_dict.get('metadata', {})
                )
                
                self.rules[rule_id] = rule
                logger.info(f"Loaded rule: {rule_id} - {rule.name}")
                
        except Exception as e:
            logger.error(f"Error loading rules from {yaml_file_path}: {str(e)}")
            
    def get_rules_for_category(self, category):
        """Get all rules that apply to a specific category"""
        return [rule for rule in self.rules.values() 
                if category in rule.applies_to_categories]
                
    def get_active_rules(self, reference_date=None):
        """Get all rules that are active as of the reference date"""
        if reference_date is None:
            reference_date = datetime.now()
            
        return [rule for rule in self.rules.values()
                if (rule.effective_date <= reference_date and 
                    (rule.expiration_date is None or 
                     rule.expiration_date > reference_date))]
```

### 2.4 Validation Engine Implementation

```python
# validation_engine/engine.py
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
import json

from .models import ValidationRule, Severity
from .rule_loader import RuleRegistry

logger = logging.getLogger(__name__)

class ValidationResult:
    def __init__(
        self,
        rule_id: str,
        passed: bool,
        value: Any,
        message: str = None,
        severity: Severity = None,
        policy_citation: str = None,
        timestamp: datetime = None,
        context: Dict[str, Any] = None
    ):
        self.rule_id = rule_id
        self.passed = passed
        self.value = value
        self.message = message
        self.severity = severity
        self.policy_citation = policy_citation
        self.timestamp = timestamp or datetime.now()
        self.context = context or {}
        
    def to_dict(self):
        return {
            "rule_id": self.rule_id,
            "passed": self.passed,
            "value": self.value,
            "message": self.message,
            "severity": self.severity.value if self.severity else None,
            "policy_citation": self.policy_citation,
            "timestamp": self.timestamp.isoformat(),
            "context": self.context
        }
        
    @classmethod
    def from_dict(cls, data):
        severity = data.get("severity")
        if severity:
            severity = Severity(severity)
            
        timestamp = data.get("timestamp")
        if timestamp:
            timestamp = datetime.fromisoformat(timestamp)
            
        return cls(
            rule_id=data.get("rule_id"),
            passed=data.get("passed"),
            value=data.get("value"),
            message=data.get("message"),
            severity=severity,
            policy_citation=data.get("policy_citation"),
            timestamp=timestamp,
            context=data.get("context")
        )

class ValidationSession:
    def __init__(self, session_id: str, project_id: str = None):
        self.session_id = session_id
        self.project_id = project_id
        self.results = []
        self.start_time = datetime.now()
        self.end_time = None
        self.metadata = {}
        
    def add_result(self, result: ValidationResult):
        self.results.append(result)
        
    def complete(self):
        self.end_time = datetime.now()
        
    def to_dict(self):
        return {
            "session_id": self.session_id,
            "project_id": self.project_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "results": [r.to_dict() for r in self.results],
            "metadata": self.metadata
        }
        
    def save(self, file_path):
        with open(file_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)

class ValidationEngine:
    def __init__(self, rule_registry: RuleRegistry):
        self.rule_registry = rule_registry
        
    def validate_entity(self, entity_data: Dict[str, Any], 
                        categories: List[str], 
                        context: Dict[str, Any] = None) -> ValidationSession:
        """
        Validate an entity against all applicable rules
        
        Args:
            entity_data: The data to validate
            categories: The categories this entity belongs to
            context: Additional context for validation
            
        Returns:
            A ValidationSession containing all validation results
        """
        session = ValidationSession(
            session_id=f"session_{datetime.now().strftime('%Y%m%d%H%M%S')}",
            project_id=context.get("project_id") if context else None
        )
        
        # Get all rules that apply to any of the specified categories
        applicable_rules = []
        for category in categories:
            applicable_rules.extend(self.rule_registry.get_rules_for_category(category))
            
        # Remove duplicates
        applicable_rules = list({rule.rule_id: rule for rule in applicable_rules}.values())
        
        # Apply each rule
        for rule in applicable_rules:
            field_name = rule.metadata.get("field_name")
            
            # If field_name is specified, validate just that field
            if field_name and field_name in entity_data:
                value = entity_data[field_name]
                passed = rule.validate(value, context)
                
                result = ValidationResult(
                    rule_id=rule.rule_id,
                    passed=passed,
                    value=value,
                    message=rule.description,
                    severity=rule.severity,
                    policy_citation=rule.policy_citation,
                    context={"field_name": field_name}
                )
                session.add_result(result)
            # Otherwise, validate the entire entity
            else:
                passed = rule.validate(entity_data, context)
                
                result = ValidationResult(
                    rule_id=rule.rule_id,
                    passed=passed,
                    value="[Entity]",
                    message=rule.description,
                    severity=rule.severity,
                    policy_citation=rule.policy_citation,
                    context={}
                )
                session.add_result(result)
                
        session.complete()
        return session
```

### 2.5 Custom Validators for FEMA-Specific Rules

```python
# validation_engine/validators/fema_validators.py
from typing import Any, Dict
import re
import datetime

def validate_procurement_documentation(value: Dict[str, Any], context: Dict[str, Any] = None) -> bool:
    """
    Validates procurement documentation against FEMA requirements
    
    Requirements:
    1. Must have contract documentation
    2. Must have evidence of competitive bidding or sole source justification
    3. Must have cost/price analysis for contracts over simplified acquisition threshold
    """
    context = context or {}
    
    # Check if required documents exist
    has_contract = value.get('contract_document') is not None
    has_bidding = value.get('bidding_document') is not None
    has_sole_source = value.get('sole_source_justification') is not None
    has_cost_analysis = value.get('cost_analysis') is not None
    
    # Check contract amount
    contract_amount = float(value.get('contract_amount', 0))
    simplified_threshold = 250000  # Current simplified acquisition threshold
    
    # Basic validation
    if not has_contract:
        return False
        
    # Check bidding requirements
    if not (has_bidding or has_sole_source):
        return False
        
    # Check cost analysis for large contracts
    if contract_amount > simplified_threshold and not has_cost_analysis:
        return False
        
    return True

def validate_insurance_requirements(value: Dict[str, Any], context: Dict[str, Any] = None) -> bool:
    """
    Validates insurance requirements for facilities in Special Flood Hazard Areas
    
    Requirements:
    1. Facilities in SFHA must have flood insurance
    2. Coverage must be at least the lesser of:
       - Maximum available under NFIP
       - Value of the facility at time of disaster
    """
    context = context or {}
    
    # Check if facility is in SFHA
    in_sfha = value.get('in_sfha', False)
    
    # If not in SFHA, no flood insurance required
    if not in_sfha:
        return True
        
    # Check if insurance exists
    has_insurance = value.get('has_flood_insurance', False)
    if not has_insurance:
        return False
        
    # Check coverage amount
    insurance_amount = float(value.get('insurance_amount', 0))
    facility_value = float(value.get('facility_value', 0))
    max_nfip = 500000  # Maximum NFIP coverage for public buildings
    
    required_coverage = min(max_nfip, facility_value)
    
    return insurance_amount >= required_coverage

def validate_environmental_historic_compliance(value: Dict[str, Any], context: Dict[str, Any] = None) -> bool:
    """
    Validates compliance with environmental and historic preservation requirements
    
    Requirements:
    1. Must have documentation of EHP review
    2. Must have any required permits
    3. Must have documentation of consultation with relevant agencies
    """
    context = context or {}
    
    # Check if EHP review is required
    ehp_required = value.get('ehp_required', True)
    
    # If EHP not required, no further validation needed
    if not ehp_required:
        return True
        
    # Check if EHP review was completed
    ehp_completed = value.get('ehp_completed', False)
    if not ehp_completed:
        return False
        
    # Check if all required permits were obtained
    required_permits = value.get('required_permits', [])
    obtained_permits = value.get('obtained_permits', [])
    
    if not all(permit in obtained_permits for permit in required_permits):
        return False
        
    return True
```

## 3. Improved Reporting Capabilities

### 3.1 Compliance Dashboard Implementation

```python
# reporting/dashboard.py
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import List, Dict, Any
import json
from datetime import datetime, timedelta

from validation_engine.models import Severity

class ComplianceDashboard:
    def __init__(self, validation_results: List[Dict[str, Any]]):
        """
        Initialize the dashboard with validation results
        
        Args:
            validation_results: List of validation result dictionaries
        """
        self.results = validation_results
        self.df = pd.DataFrame(validation_results)
        
        # Convert timestamp strings to datetime objects
        if 'timestamp' in self.df.columns:
            self.df['timestamp'] = pd.to_datetime(self.df['timestamp'])
            
        # Convert severity strings to Severity enum values
        if 'severity' in self.df.columns:
            self.df['severity'] = self.df['severity'].apply(
                lambda x: x if pd.isna(x) else Severity(x)
            )
    
    def generate_summary_stats(self):
        """Generate summary statistics for compliance results"""
        total_rules = len(self.df)
        passed_rules = len(self.df[self.df['passed'] == True])
        failed_rules = total_rules - passed_rules
        
        # Count by severity
        severity_counts = {}
        if 'severity' in self.df.columns:
            for severity in Severity:
                count = len(self.df[(self.df['severity'] == severity) & 
                                    (self.df['passed'] == False)])
                severity_counts[severity.value] = count
                
        return {
            "total_rules": total_rules,
            "passed_rules": passed_rules,
            "failed_rules": failed_rules,
            "compliance_rate": (passed_rules / total_rules) * 100 if total_rules > 0 else 0,
            "severity_counts": severity_counts
        }
    
    def generate_compliance_trend(self, days=30):
        """Generate compliance trend over time"""
        if 'timestamp' not in self.df.columns:
            return None
            
        # Filter to last N days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        filtered_df = self.df[self.df['timestamp'] >= start_date]
        
        # Group by day and calculate compliance rate
        filtered_df['date'] = filtered_df['timestamp'].dt.date
        daily_stats = filtered_df.groupby('date').agg(
            total=('rule_id', 'count'),
            passed=('passed', lambda x: sum(x == True))
        ).reset_index()
        
        daily_stats['compliance_rate'] = (daily_stats['passed'] / 
                                         daily_stats['total']) * 100
                                         
        return daily_stats
    
    def generate_policy_compliance_chart(self):
        """Generate chart showing compliance by policy source"""
        if 'policy_citation' not in self.df.columns:
            return None
            
        # Extract policy source from citation
        self.df['policy_source'] = self.df['policy_citation'].apply(
            lambda x: x.split(',')[0] if isinstance(x, str) and ',' in x else x
        )
        
        # Group by policy source
        policy_stats = self.df.groupby('policy_source').agg(
            total=('rule_id', 'count'),
            passed=('passed', lambda x: sum(x == True))
        ).reset_index()
        
        policy_stats['compliance_rate'] = (policy_stats['passed'] / 
                                          policy_stats['total']) * 100
                                          
        # Create chart
        fig = px.bar(
            policy_stats,
            x='policy_source',
            y='compliance_rate',
            title='Compliance Rate by Policy Source',
            labels={'policy_source': 'Policy Source', 
                   'compliance_rate': 'Compliance Rate (%)'}
        )
        
        return fig
    
    def generate_severity_distribution_chart(self):
        """Generate chart showing distribution of findings by severity"""
        if 'severity' not in self.df.columns:
            return None
            
        # Filter to failed validations
        failed_df = self.df[self.df['passed'] == False]
        
        # Count by severity
        severity_counts = failed_df['severity'].value_counts().reset_index()
        severity_counts.columns = ['severity', 'count']
        
        # Convert severity enum to string
        severity_counts['severity'] = severity_counts['severity'].apply(
            lambda x: x.value if hasattr(x, 'value') else str(x)
        )
        
        # Create chart
        fig = px.pie(
            severity_counts,
            values='count',
            names='severity',
            title='Finding Distribution by Severity',
            color='severity',
            color_discrete_map={
                'info': 'blue',
                'warning': 'orange',
                'error': 'red',
                'critical': 'darkred'
            }
        )
        
        return fig
    
    def export_to_html(self, output_path):
        """Export dashboard to HTML file"""
        # Generate all charts
        summary = self.generate_summary_stats()
        trend_data = self.generate_compliance_trend()
        policy_chart = self.generate_policy_compliance_chart()
        severity_chart = self.generate_severity_distribution_chart()
        
        # Create trend chart if data exists
        trend_chart = None
        if trend_data is not None:
            trend_chart = px.line(
                trend_data,
                x='date',
                y='compliance_rate',
                title='Compliance Rate Trend',
                labels={'date': 'Date', 'compliance_rate': 'Compliance Rate (%)'}
            )
        
        # Combine into dashboard
        dashboard_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>ComplianceMax FEMA Compliance Dashboard</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ display: flex; justify-content: space-around; margin-bottom: 30px; }}
                .summary-card {{ 
                    border: 1px solid #ddd; 
                    border-radius: 5px; 
                    padding: 15px; 
                    width: 200px; 
                    text-align: center; 
                }}
                .chart-container {{ margin-bottom: 30px; }}
                .critical {{ color: darkred; }}
                .error {{ color: red; }}
                .warning {{ color: orange; }}
                .info {{ color: blue; }}
            </style>
        </head>
        <body>
            <h1>ComplianceMax FEMA Compliance Dashboard</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <div class="summary-card">
                    <h3>Compliance Rate</h3>
                    <h2>{summary['compliance_rate']:.1f}%</h2>
                    <p>{summary['passed_rules']} of {summary['total_rules']} checks passed</p>
                </div>
                <div class="summary-card">
                    <h3>Critical Findings</h3>
                    <h2 class="critical">{summary['severity_counts'].get('critical', 0)}</h2>
                </div>
                <div class="summary-card">
                    <h3>Error Findings</h3>
                    <h2 class="error">{summary['severity_counts'].get('error', 0)}</h2>
                </div>
                <div class="summary-card">
                    <h3>Warning Findings</h3>
                    <h2 class="warning">{summary['severity_counts'].get('warning', 0)}</h2>
                </div>
            </div>
        """
        
        # Add trend chart if available
        if trend_chart is not None:
            dashboard_html += f"""
            <div class="chart-container">
                <div id="trend-chart"></div>
                <script>
                    var trendData = {trend_chart.to_json()};
                    Plotly.newPlot('trend-chart', trendData.data, trendData.layout);
                </script>
            </div>
            """
            
        # Add policy compliance chart if available
        if policy_chart is not None:
            dashboard_html += f"""
            <div class="chart-container">
                <div id="policy-chart"></div>
                <script>
                    var policyData = {policy_chart.to_json()};
                    Plotly.newPlot('policy-chart', policyData.data, policyData.layout);
                </script>
            </div>
            """
            
        # Add severity distribution chart if available
        if severity_chart is not None:
            dashboard_html += f"""
            <div class="chart-container">
                <div id="severity-chart"></div>
                <script>
                    var severityData = {severity_chart.to_json()};
                    Plotly.newPlot('severity-chart', severityData.data, severityData.layout);
                </script>
            </div>
            """
            
        # Close HTML
        dashboard_html += """
        </body>
        </html>
        """
        
        # Write to file
        with open(output_path, 'w') as f:
            f.write(dashboard_html)
```

### 3.2 PDF Report Generator

```python
# reporting/pdf_generator.py
import os
from datetime import datetime
from typing import List, Dict, Any
import json
import pandas as pd
from fpdf import FPDF

class ComplianceReportPDF(FPDF):
    def __init__(self, title="FEMA Compliance Report"):
        super().__init__()
        self.title = title
        self.set_auto_page_break(auto=True, margin=15)
        
    def header(self):
        # Logo
        # self.image('logo.png', 10, 8, 33)
        # Title
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, self.title, 0, 1, 'C')
        # Line break
        self.ln(10)
        
    def footer(self):
        # Position at 1.5 cm from bottom
        self.set_y(-15)
        # Arial italic 8
        self.set_font('Arial', 'I', 8)
        # Page number
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')
        
    def chapter_title(self, title):
        self.set_font('Arial', 'B', 12)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(4)
        
    def chapter_body(self, body):
        self.set_font('Arial', '', 10)
        self.multi_cell(0, 5, body)
        self.ln()
        
    def add_table(self, headers, data):
        # Table headers
        self.set_font('Arial', 'B', 10)
        line_height = 7
        col_widths = [40, 100, 40]  # Adjust as needed
        
        for i, header in enumerate(headers):
            self.cell(col_widths[i], line_height, header, 1, 0, 'C')
        self.ln()
        
        # Table data
        self.set_font('Arial', '', 10)
        for row in data:
            for i, cell in enumerate(row):
                self.cell(col_widths[i], line_height, str(cell), 1, 0, 'L')
            self.ln()

class ComplianceReportGenerator:
    def __init__(self, validation_results: List[Dict[str, Any]], project_info: Dict[str, Any]):
        """
        Initialize the report generator
        
        Args:
            validation_results: List of validation result dictionaries
            project_info: Dictionary containing project information
        """
        self.results = validation_results
        self.project_info = project_info
        self.df = pd.DataFrame(validation_results)
        
    def generate_pdf_report(self, output_path):
        """Generate a PDF compliance report"""
        pdf = ComplianceReportPDF(title="FEMA Compliance Report")
        
        # Add cover page
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        pdf.cell(0, 10, "FEMA Public Assistance", 0, 1, 'C')
        pdf.cell(0, 10, "Compliance Validation Report", 0, 1, 'C')
        pdf.ln(20)
        
        # Project information
        pdf.set_font('Arial', 'B', 12)
        pdf.cell(0, 10, "Project Information", 0, 1, 'L')
        pdf.set_font('Arial', '', 10)
        pdf.cell(40, 7, "Project ID:", 0, 0)
        pdf.cell(0, 7, self.project_info.get('project_id', 'N/A'), 0, 1)
        pdf.cell(40, 7, "Applicant:", 0, 0)
        pdf.cell(0, 7, self.project_info.get('applicant_name', 'N/A'), 0, 1)
        pdf.cell(40, 7, "Disaster #:", 0, 0)
        pdf.cell(0, 7, self.project_info.get('disaster_number', 'N/A'), 0, 1)
        pdf.cell(40, 7, "Report Date:", 0, 0)
        pdf.cell(0, 7, datetime.now().strftime('%Y-%m-%d'), 0, 1)
        
        # Executive summary
        pdf.add_page()
        pdf.chapter_title("Executive Summary")
        
        # Calculate summary statistics
        total_rules = len(self.df)
        passed_rules = len(self.df[self.df['passed'] == True])
        failed_rules = total_rules - passed_rules
        compliance_rate = (passed_rules / total_rules) * 100 if total_rules > 0 else 0
        
        summary_text = f"""
        This report provides a comprehensive assessment of compliance with FEMA Public Assistance policies and regulations for the project {self.project_info.get('project_id', 'N/A')}.
        
        Overall Compliance Rate: {compliance_rate:.1f}%
        
        Total Rules Evaluated: {total_rules}
        Passed: {passed_rules}
        Failed: {failed_rules}
        
        The following sections provide detailed findings and recommendations for addressing compliance issues.
        """
        
        pdf.chapter_body(summary_text)
        
        # Findings by severity
        pdf.add_page()
        pdf.chapter_title("Critical Findings")
        
        # Filter critical findings
        if 'severity' in self.df.columns:
            critical_findings = self.df[(self.df['passed'] == False) & 
                                       (self.df['severity'] == 'critical')]
            
            if len(critical_findings) > 0:
                # Prepare data for table
                headers = ["Rule ID", "Description", "Policy Citation"]
                data = []
                
                for _, row in critical_findings.iterrows():
                    data.append([
                        row.get('rule_id', 'N/A'),
                        row.get('message', 'N/A'),
                        row.get('policy_citation', 'N/A')
                    ])
                
                pdf.add_table(headers, data)
            else:
                pdf.chapter_body("No critical findings identified.")
        
        # Add sections for other severities
        pdf.add_page()
        pdf.chapter_title("Error Findings")
        # Similar implementation for error findings
        
        pdf.add_page()
        pdf.chapter_title("Warning Findings")
        # Similar implementation for warning findings
        
        # Recommendations
        pdf.add_page()
        pdf.chapter_title("Recommendations")
        
        recommendations_text = """
        Based on the compliance validation results, the following recommendations are provided:
        
        1. Address all critical findings immediately to ensure compliance with FEMA requirements.
        
        2. Develop corrective action plans for error-level findings within 30 days.
        
        3. Review warning-level findings and implement improvements where feasible.
        
        4. Maintain comprehensive documentation for all corrective actions taken.
        
        5. Schedule a follow-up compliance validation within 60 days to verify that issues have been resolved.
        """
        
        pdf.chapter_body(recommendations_text)
        
        # Save the PDF
        pdf.output(output_path)
```

## 4. Audit Readiness Features

### 4.1 Immutable Audit Log Implementation

```python
# audit/audit_log.py
import json
import hashlib
import os
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
import uuid

class AuditLogEntry:
    def __init__(
        self,
        action: str,
        entity_type: str,
        entity_id: str,
        user_id: str,
        timestamp: Optional[datetime] = None,
        details: Optional[Dict[str, Any]] = None,
        previous_hash: Optional[str] = None
    ):
        self.entry_id = str(uuid.uuid4())
        self.action = action
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.user_id = user_id
        self.timestamp = timestamp or datetime.now()
        self.details = details or {}
        self.previous_hash = previous_hash
        self.hash = self._calculate_hash()
        
    def _calculate_hash(self) -> str:
        """Calculate a hash of this entry's contents"""
        data = {
            "entry_id": self.entry_id,
            "action": self.action,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "user_id": self.user_id,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "previous_hash": self.previous_hash
        }
        
        # Convert to JSON string and hash
        json_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(json_str.encode()).hexdigest()
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert entry to dictionary"""
        return {
            "entry_id": self.entry_id,
            "action": self.action,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "user_id": self.user_id,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "previous_hash": self.previous_hash,
            "hash": self.hash
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AuditLogEntry':
        """Create entry from dictionary"""
        entry = cls(
            action=data["action"],
            entity_type=data["entity_type"],
            entity_id=data["entity_id"],
            user_id=data["user_id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            details=data["details"],
            previous_hash=data["previous_hash"]
        )
        entry.entry_id = data["entry_id"]
        entry.hash = data["hash"]
        return entry

class ImmutableAuditLog:
    def __init__(self, log_dir: str):
        """
        Initialize the audit log
        
        Args:
            log_dir: Directory to store audit log files
        """
        self.log_dir = log_dir
        self.current_file = None
        self.last_hash = None
        
        # Create log directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
        
        # Initialize or load existing log
        self._initialize()
        
    def _initialize(self):
        """Initialize the audit log or load the latest state"""
        log_files = sorted([f for f in os.listdir(self.log_dir) 
                           if f.startswith("audit_log_")])
        
        if not log_files:
            # Create new log file
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            self.current_file = os.path.join(self.log_dir, f"audit_log_{timestamp}.json")
            self.last_hash = None
            
            # Initialize with empty array
            with open(self.current_file, 'w') as f:
                json.dump([], f)
        else:
            # Load the latest log file
            self.current_file = os.path.join(self.log_dir, log_files[-1])
            
            # Get the last hash
            try:
                with open(self.current_file, 'r') as f:
                    entries = json.load(f)
                    if entries:
                        self.last_hash = entries[-1]["hash"]
                    else:
                        self.last_hash = None
            except Exception as e:
                print(f"Error loading audit log: {str(e)}")
                # Create new log file if there's an error
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                self.current_file = os.path.join(self.log_dir, f"audit_log_{timestamp}.json")
                self.last_hash = None
                with open(self.current_file, 'w') as f:
                    json.dump([], f)
    
    def add_entry(self, 
                 action: str, 
                 entity_type: str, 
                 entity_id: str, 
                 user_id: str,
                 details: Optional[Dict[str, Any]] = None) -> AuditLogEntry:
        """
        Add a new entry to the audit log
        
        Args:
            action: The action performed (e.g., "create", "update", "validate")
            entity_type: The type of entity (e.g., "project", "document")
            entity_id: The ID of the entity
            user_id: The ID of the user who performed the action
            details: Additional details about the action
            
        Returns:
            The created audit log entry
        """
        # Create new entry
        entry = AuditLogEntry(
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            user_id=user_id,
            details=details,
            previous_hash=self.last_hash
        )
        
        # Update last hash
        self.last_hash = entry.hash
        
        # Append to log file
        try:
            with open(self.current_file, 'r') as f:
                entries = json.load(f)
                
            entries.append(entry.to_dict())
            
            with open(self.current_file, 'w') as f:
                json.dump(entries, f, indent=2)
                
        except Exception as e:
            print(f"Error adding audit log entry: {str(e)}")
            raise
            
        return entry
    
    def get_entries(self, 
                   entity_type: Optional[str] = None,
                   entity_id: Optional[str] = None,
                   user_id: Optional[str] = None,
                   action: Optional[str] = None,
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[AuditLogEntry]:
        """
        Get audit log entries with optional filtering
        
        Args:
            entity_type: Filter by entity type
            entity_id: Filter by entity ID
            user_id: Filter by user ID
            action: Filter by action
            start_time: Filter by start time
            end_time: Filter by end time
            
        Returns:
            List of matching audit log entries
        """
        try:
            with open(self.current_file, 'r') as f:
                entries_dict = json.load(f)
                
            # Convert to AuditLogEntry objects
            entries = [AuditLogEntry.from_dict(e) for e in entries_dict]
            
            # Apply filters
            if entity_type:
                entries = [e for e in entries if e.entity_type == entity_type]
            if entity_id:
                entries = [e for e in entries if e.entity_id == entity_id]
            if user_id:
                entries = [e for e in entries if e.user_id == user_id]
            if action:
                entries = [e for e in entries if e.action == action]
            if start_time:
                entries = [e for e in entries if e.timestamp >= start_time]
            if end_time:
                entries = [e for e in entries if e.timestamp <= end_time]
                
            return entries
            
        except Exception as e:
            print(f"Error retrieving audit log entries: {str(e)}")
            return []
    
    def verify_integrity(self) -> bool:
        """
        Verify the integrity of the audit log
        
        Returns:
            True if the log is valid, False otherwise
        """
        try:
            with open(self.current_file, 'r') as f:
                entries_dict = json.load(f)
                
            if not entries_dict:
                return True
                
            # Verify each entry's hash
            previous_hash = None
            for entry_dict in entries_dict:
                entry = AuditLogEntry.from_dict(entry_dict)
                
                # Check previous hash
                if entry.previous_hash != previous_hash:
                    return False
                    
                # Check entry's hash
                calculated_hash = entry._calculate_hash()
                if calculated_hash != entry.hash:
                    return False
                    
                previous_hash = entry.hash
                
            return True
            
        except Exception as e:
            print(f"Error verifying audit log integrity: {str(e)}")
            return False
```

### 4.2 Evidence Snapshot Implementation

```python
# audit/evidence_snapshot.py
import os
import json
import hashlib
import shutil
import zipfile
from datetime import datetime
from typing import Dict, Any, List, Optional
import uuid

class EvidenceSnapshot:
    def __init__(self, snapshot_dir: str):
        """
        Initialize the evidence snapshot manager
        
        Args:
            snapshot_dir: Directory to store evidence snapshots
        """
        self.snapshot_dir = snapshot_dir
        
        # Create snapshot directory if it doesn't exist
        os.makedirs(snapshot_dir, exist_ok=True)
        
    def create_snapshot(self, 
                       project_id: str,
                       user_id: str,
                       files: List[str],
                       metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Create a new evidence snapshot
        
        Args:
            project_id: The ID of the project
            user_id: The ID of the user creating the snapshot
            files: List of file paths to include in the snapshot
            metadata: Additional metadata about the snapshot
            
        Returns:
            The ID of the created snapshot
        """
        # Generate snapshot ID
        snapshot_id = str(uuid.uuid4())
        
        # Create snapshot directory
        snapshot_path = os.path.join(self.snapshot_dir, snapshot_id)
        os.makedirs(snapshot_path, exist_ok=True)
        
        # Copy files to snapshot directory
        file_hashes = {}
        for file_path in files:
            if os.path.exists(file_path):
                # Calculate file hash
                file_hash = self._calculate_file_hash(file_path)
                file_hashes[file_path] = file_hash
                
                # Copy file to snapshot directory
                filename = os.path.basename(file_path)
                dest_path = os.path.join(snapshot_path, filename)
                shutil.copy2(file_path, dest_path)
        
        # Create manifest
        manifest = {
            "snapshot_id": snapshot_id,
            "project_id": project_id,
            "user_id": user_id,
            "timestamp": datetime.now().isoformat(),
            "files": file_hashes,
            "metadata": metadata or {}
        }
        
        # Write manifest to snapshot directory
        manifest_path = os.path.join(snapshot_path, "manifest.json")
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
            
        # Create ZIP archive
        zip_path = os.path.join(self.snapshot_dir, f"{snapshot_id}.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(snapshot_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, snapshot_path)
                    zipf.write(file_path, arcname)
        
        # Calculate and store ZIP hash
        zip_hash = self._calculate_file_hash(zip_path)
        hash_path = os.path.join(self.snapshot_dir, f"{snapshot_id}.hash")
        with open(hash_path, 'w') as f:
            f.write(zip_hash)
            
        return snapshot_id
    
    def get_snapshot_info(self, snapshot_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a snapshot
        
        Args:
            snapshot_id: The ID of the snapshot
            
        Returns:
            Dictionary containing snapshot information, or None if not found
        """
        manifest_path = os.path.join(self.snapshot_dir, snapshot_id, "manifest.json")
        if not os.path.exists(manifest_path):
            return None
            
        try:
            with open(manifest_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading snapshot manifest: {str(e)}")
            return None
    
    def verify_snapshot(self, snapshot_id: str) -> bool:
        """
        Verify the integrity of a snapshot
        
        Args:
            snapshot_id: The ID of the snapshot
            
        Returns:
            True if the snapshot is valid, False otherwise
        """
        # Check if snapshot exists
        snapshot_path = os.path.join(self.snapshot_dir, snapshot_id)
        if not os.path.exists(snapshot_path):
            return False
            
        # Check manifest
        manifest_path = os.path.join(snapshot_path, "manifest.json")
        if not os.path.exists(manifest_path):
            return False
            
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
                
            # Verify each file
            for file_path, expected_hash in manifest["files"].items():
                filename = os.path.basename(file_path)
                snapshot_file_path = os.path.join(snapshot_path, filename)
                
                if not os.path.exists(snapshot_file_path):
                    return False
                    
                actual_hash = self._calculate_file_hash(snapshot_file_path)
                if actual_hash != expected_hash:
                    return False
                    
            # Verify ZIP archive
            zip_path = os.path.join(self.snapshot_dir, f"{snapshot_id}.zip")
            hash_path = os.path.join(self.snapshot_dir, f"{snapshot_id}.hash")
            
            if not os.path.exists(zip_path) or not os.path.exists(hash_path):
                return False
                
            with open(hash_path, 'r') as f:
                expected_zip_hash = f.read().strip()
                
            actual_zip_hash = self._calculate_file_hash(zip_path)
            if actual_zip_hash != expected_zip_hash:
                return False
                
            return True
            
        except Exception as e:
            print(f"Error verifying snapshot: {str(e)}")
            return False
    
    def list_snapshots(self, 
                      project_id: Optional[str] = None,
                      user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List available snapshots with optional filtering
        
        Args:
            project_id: Filter by project ID
            user_id: Filter by user ID
            
        Returns:
            List of snapshot information dictionaries
        """
        snapshots = []
        
        for item in os.listdir(self.snapshot_dir):
            item_path = os.path.join(self.snapshot_dir, item)
            
            # Check if it's a directory (snapshot)
            if os.path.isdir(item_path):
                manifest_path = os.path.join(item_path, "manifest.json")
                
                if os.path.exists(manifest_path):
                    try:
                        with open(manifest_path, 'r') as f:
                            manifest = json.load(f)
                            
                        # Apply filters
                        if project_id and manifest.get("project_id") != project_id:
                            continue
                            
                        if user_id and manifest.get("user_id") != user_id:
                            continue
                            
                        snapshots.append(manifest)
                            
                    except Exception as e:
                        print(f"Error loading snapshot manifest: {str(e)}")
        
        # Sort by timestamp (newest first)
        snapshots.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
        
        return snapshots
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of a file"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            # Read and update hash in chunks
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
                
        return sha256_hash.hexdigest()
```

### 4.3 User Action Trace Implementation

```python
# audit/user_action_trace.py
import json
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
import uuid

class UserAction:
    def __init__(
        self,
        action_id: Optional[str] = None,
        user_id: str = "",
        action_type: str = "",
        entity_type: str = "",
        entity_id: str = "",
        timestamp: Optional[datetime] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        self.action_id = action_id or str(uuid.uuid4())
        self.user_id = user_id
        self.action_type = action_type
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.timestamp = timestamp or datetime.now()
        self.details = details or {}
        self.ip_address = ip_address
        self.session_id = session_id
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert action to dictionary"""
        return {
            "action_id": self.action_id,
            "user_id": self.user_id,
            "action_type": self.action_type,
            "entity_type": self.entity_type,
            "entity_id": self.entity_id,
            "timestamp": self.timestamp.isoformat(),
            "details": self.details,
            "ip_address": self.ip_address,
            "session_id": self.session_id
        }
        
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserAction':
        """Create action from dictionary"""
        return cls(
            action_id=data.get("action_id"),
            user_id=data.get("user_id", ""),
            action_type=data.get("action_type", ""),
            entity_type=data.get("entity_type", ""),
            entity_id=data.get("entity_id", ""),
            timestamp=datetime.fromisoformat(data.get("timestamp")) if data.get("timestamp") else None,
            details=data.get("details", {}),
            ip_address=data.get("ip_address"),
            session_id=data.get("session_id")
        )

class UserActionTracer:
    def __init__(self, trace_dir: str):
        """
        Initialize the user action tracer
        
        Args:
            trace_dir: Directory to store user action traces
        """
        self.trace_dir = trace_dir
        
        # Create trace directory if it doesn't exist
        os.makedirs(trace_dir, exist_ok=True)
        
    def record_action(self,
                     user_id: str,
                     action_type: str,
                     entity_type: str,
                     entity_id: str,
                     details: Optional[Dict[str, Any]] = None,
                     ip_address: Optional[str] = None,
                     session_id: Optional[str] = None) -> UserAction:
        """
        Record a user action
        
        Args:
            user_id: The ID of the user
            action_type: The type of action (e.g., "view", "edit", "approve")
            entity_type: The type of entity (e.g., "project", "document")
            entity_id: The ID of the entity
            details: Additional details about the action
            ip_address: The IP address of the user
            session_id: The session ID of the user
            
        Returns:
            The recorded user action
        """
        # Create action
        action = UserAction(
            user_id=user_id,
            action_type=action_type,
            entity_type=entity_type,
            entity_id=entity_id,
            details=details,
            ip_address=ip_address,
            session_id=session_id
        )
        
        # Determine file path
        date_str = action.timestamp.strftime("%Y-%m-%d")
        file_path = os.path.join(self.trace_dir, f"actions_{date_str}.json")
        
        # Append to file
        try:
            # Load existing actions
            actions = []
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    actions = json.load(f)
                    
            # Add new action
            actions.append(action.to_dict())
            
            # Write back to file
            with open(file_path, 'w') as f:
                json.dump(actions, f, indent=2)
                
        except Exception as e:
            print(f"Error recording user action: {str(e)}")
            raise
            
        return action
    
    def get_actions(self,
                   user_id: Optional[str] = None,
                   action_type: Optional[str] = None,
                   entity_type: Optional[str] = None,
                   entity_id: Optional[str] = None,
                   start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   session_id: Optional[str] = None) -> List[UserAction]:
        """
        Get user actions with optional filtering
        
        Args:
            user_id: Filter by user ID
            action_type: Filter by action type
            entity_type: Filter by entity type
            entity_id: Filter by entity ID
            start_date: Filter by start date
            end_date: Filter by end date
            session_id: Filter by session ID
            
        Returns:
            List of matching user actions
        """
        actions = []
        
        # Determine date range
        if start_date is None:
            # Default to last 30 days
            start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = start_date.replace(day=1)
            
        if end_date is None:
            end_date = datetime.now()
            
        # Get all files in date range
        current_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
        while current_date <= end_date:
            date_str = current_date.strftime("%Y-%m-%d")
            file_path = os.path.join(self.trace_dir, f"actions_{date_str}.json")
            
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        actions_data = json.load(f)
                        
                    for action_data in actions_data:
                        action = UserAction.from_dict(action_data)
                        
                        # Apply filters
                        if user_id and action.user_id != user_id:
                            continue
                            
                        if action_type and action.action_type != action_type:
                            continue
                            
                        if entity_type and action.entity_type != entity_type:
                            continue
                            
                        if entity_id and action.entity_id != entity_id:
                            continue
                            
                        if session_id and action.session_id != session_id:
                            continue
                            
                        if start_date and action.timestamp < start_date:
                            continue
                            
                        if end_date and action.timestamp > end_date:
                            continue
                            
                        actions.append(action)
                        
                except Exception as e:
                    print(f"Error loading user actions from {file_path}: {str(e)}")
            
            # Move to next day
            current_date = current_date.replace(day=current_date.day + 1)
            
        # Sort by timestamp
        actions.sort(key=lambda x: x.timestamp)
        
        return actions
    
    def get_user_session_history(self, session_id: str) -> List[UserAction]:
        """
        Get all actions for a specific session
        
        Args:
            session_id: The session ID
            
        Returns:
            List of user actions in the session
        """
        return self.get_actions(session_id=session_id)
    
    def get_entity_history(self, entity_type: str, entity_id: str) -> List[UserAction]:
        """
        Get all actions for a specific entity
        
        Args:
            entity_type: The type of entity
            entity_id: The ID of the entity
            
        Returns:
            List of user actions on the entity
        """
        return self.get_actions(entity_type=entity_type, entity_id=entity_id)
```

## 5. Policy Update Mechanism

### 5.1 FEMA Policy Monitor Implementation

```python
# policy_update/policy_monitor.py
import requests
import feedparser
import hashlib
import os
import json
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from bs4 import BeautifulSoup
import logging

logger = logging.getLogger(__name__)

class FEMAPolicyMonitor:
    def __init__(self, cache_dir: str):
        """
        Initialize the FEMA policy monitor
        
        Args:
            cache_dir: Directory to store cached policy data
        """
        self.cache_dir = cache_dir
        
        # Create cache directory if it doesn't exist
        os.makedirs(cache_dir, exist_ok=True)
        
        # FEMA policy sources
        self.sources = {
            "pa_policy_guide": {
                "name": "Public Assistance Program and Policy Guide",
                "url": "https://www.fema.gov/assistance/public/policy-guidance-fact-sheets",
                "type": "web",
                "selector": "a[href*='pappg']"
            },
            "pa_fact_sheets": {
                "name": "Public Assistance Fact Sheets",
                "url": "https://www.fema.gov/assistance/public/policy-guidance-fact-sheets",
                "type": "web",
                "selector": "a[href*='fact-sheet']"
            },
            "federal_register": {
                "name": "Federal Register - FEMA Rules",
                "url": "https://www.federalregister.gov/agencies/federal-emergency-management-agency",
                "type": "web",
                "selector": "a.document-title"
            },
            "fema_news": {
                "name": "FEMA News Releases",
                "url": "https://www.fema.gov/about/news-multimedia/news-releases/rss",
                "type": "rss"
            }
        }
        
    def check_for_updates(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Check all sources for policy updates
        
        Returns:
            Dictionary mapping source IDs to lists of updates
        """
        updates = {}
        
        for source_id, source in self.sources.items():
            try:
                if source["type"] == "web":
                    source_updates = self._check_web_source(source_id, source)
                elif source["type"] == "rss":
                    source_updates = self._check_rss_source(source_id, source)
                else:
                    logger.warning(f"Unknown source type: {source['type']}")
                    continue
                    
                if source_updates:
                    updates[source_id] = source_updates
                    
            except Exception as e:
                logger.error(f"Error checking source {source_id}: {str(e)}")
                
        return updates
    
    def _check_web_source(self, source_id: str, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check a web source for updates"""
        # Load cached data
        cache_file = os.path.join(self.cache_dir, f"{source_id}.json")
        cached_data = self._load_cache(cache_file)
        
        # Fetch current data
        response = requests.get(source["url"])
        if response.status_code != 200:
            logger.error(f"Failed to fetch {source['url']}: {response.status_code}")
            return []
            
        # Parse HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Extract links matching selector
        links = soup.select(source["selector"])
        
        # Process links
        current_data = {}
        updates = []
        
        for link in links:
            href = link.get('href')
            if not href:
                continue
                
            # Make absolute URL if needed
            if href.startswith('/'):
                href = f"https://www.fema.gov{href}"
                
            title = link.get_text().strip()
            if not title:
                title = href.split('/')[-1]
                
            # Calculate hash of link content
            link_hash = hashlib.md5(f"{href}|{title}".encode()).hexdigest()
            
            # Store in current data
            current_data[link_hash] = {
                "url": href,
                "title": title,
                "last_checked": datetime.now().isoformat()
            }
            
            # Check if this is new or updated
            if link_hash not in cached_data:
                updates.append({
                    "source_id": source_id,
                    "source_name": source["name"],
                    "url": href,
                    "title": title,
                    "type": "new",
                    "timestamp": datetime.now().isoformat()
                })
                
        # Save current data to cache
        self._save_cache(cache_file, current_data)
        
        return updates
    
    def _check_rss_source(self, source_id: str, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check an RSS source for updates"""
        # Load cached data
        cache_file = os.path.join(self.cache_dir, f"{source_id}.json")
        cached_data = self._load_cache(cache_file)
        
        # Fetch RSS feed
        feed = feedparser.parse(source["url"])
        
        # Process entries
        current_data = {}
        updates = []
        
        for entry in feed.entries:
            # Extract data
            entry_id = entry.get('id', entry.get('link', ''))
            title = entry.get('title', '')
            link = entry.get('link', '')
            published = entry.get('published', datetime.now().isoformat())
            
            # Calculate hash
            entry_hash = hashlib.md5(entry_id.encode()).hexdigest()
            
            # Store in current data
            current_data[entry_hash] = {
                "id": entry_id,
                "url": link,
                "title": title,
                "published": published,
                "last_checked": datetime.now().isoformat()
            }
            
            # Check if this is new
            if entry_hash not in cached_data:
                # Check if it's related to Public Assistance
                if self._is_pa_related(title) or self._is_pa_related(entry.get('summary', '')):
                    updates.append({
                        "source_id": source_id,
                        "source_name": source["name"],
                        "url": link,
                        "title": title,
                        "type": "new",
                        "timestamp": published
                    })
                    
        # Save current data to cache
        self._save_cache(cache_file, current_data)
        
        return updates
    
    def _is_pa_related(self, text: str) -> bool:
        """Check if text is related to Public Assistance"""
        pa_keywords = [
            "public assistance",
            "PA program",
            "PAPPG",
            "disaster recovery",
            "44 CFR",
            "Stafford Act",
            "FEMA grants",
            "project worksheet"
        ]
        
        text = text.lower()
        
        for keyword in pa_keywords:
            if keyword.lower() in text:
                return True
                
        return False
    
    def _load_cache(self, cache_file: str) -> Dict[str, Any]:
        """Load cached data from file"""
        if not os.path.exists(cache_file):
            return {}
            
        try:
            with open(cache_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading cache file {cache_file}: {str(e)}")
            return {}
    
    def _save_cache(self, cache_file: str, data: Dict[str, Any]):
        """Save data to cache file"""
        try:
            with open(cache_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving cache file {cache_file}: {str(e)}")

class PolicyDiffGenerator:
    def __init__(self):
        """Initialize the policy diff generator"""
        pass
        
    def generate_diff(self, old_text: str, new_text: str) -> List[Dict[str, Any]]:
        """
        Generate a semantic diff between old and new policy text
        
        Args:
            old_text: The old policy text
            new_text: The new policy text
            
        Returns:
            List of differences with context
        """
        # This is a simplified implementation
        # In a real system, you would use a more sophisticated diff algorithm
        
        # Split into paragraphs
        old_paragraphs = old_text.split('\n\n')
        new_paragraphs = new_text.split('\n\n')
        
        # Find added, removed, and changed paragraphs
        diffs = []
        
        # Check for removed paragraphs
        for i, old_para in enumerate(old_paragraphs):
            if old_para.strip() and old_para not in new_paragraphs:
                diffs.append({
                    "type": "removed",
                    "content": old_para,
                    "position": i
                })
                
        # Check for added paragraphs
        for i, new_para in enumerate(new_paragraphs):
            if new_para.strip() and new_para not in old_paragraphs:
                diffs.append({
                    "type": "added",
                    "content": new_para,
                    "position": i
                })
                
        return diffs
```

### 5.2 Policy Update Notification System

```python
# policy_update/notification_system.py
import smtplib
import os
import json
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

class PolicyUpdateNotifier:
    def __init__(self, config_file: str):
        """
        Initialize the policy update notifier
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = config_file
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if not os.path.exists(self.config_file):
            # Return default configuration
            return {
                "smtp": {
                    "server": "smtp.example.com",
                    "port": 587,
                    "username": "",
                    "password": "",
                    "use_tls": True
                },
                "from_email": "<EMAIL>",
                "subscribers": []
            }
            
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading config file {self.config_file}: {str(e)}")
            return {}
    
    def _save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving config file {self.config_file}: {str(e)}")
    
    def add_subscriber(self, email: str, name: Optional[str] = None, 
                      roles: Optional[List[str]] = None):
        """
        Add a subscriber to policy updates
        
        Args:
            email: Email address of the subscriber
            name: Name of the subscriber
            roles: List of roles (used for filtering notifications)
        """
        # Initialize subscribers list if needed
        if "subscribers" not in self.config:
            self.config["subscribers"] = []
            
        # Check if subscriber already exists
        for subscriber in self.config["subscribers"]:
            if subscriber.get("email") == email:
                # Update existing subscriber
                if name:
                    subscriber["name"] = name
                if roles:
                    subscriber["roles"] = roles
                self._save_config()
                return
                
        # Add new subscriber
        self.config["subscribers"].append({
            "email": email,
            "name": name or "",
            "roles": roles or ["all"],
            "added_date": datetime.now().isoformat()
        })
        
        self._save_config()
    
    def remove_subscriber(self, email: str):
        """
        Remove a subscriber
        
        Args:
            email: Email address of the subscriber to remove
        """
        if "subscribers" not in self.config:
            return
            
        self.config["subscribers"] = [
            s for s in self.config["subscribers"] 
            if s.get("email") != email
        ]
        
        self._save_config()
    
    def notify_updates(self, updates: Dict[str, List[Dict[str, Any]]]):
        """
        Send notifications about policy updates
        
        Args:
            updates: Dictionary mapping source IDs to lists of updates
        """
        if not updates:
            logger.info("No updates to notify about")
            return
            
        # Check if we have subscribers
        if "subscribers" not in self.config or not self.config["subscribers"]:
            logger.warning("No subscribers configured")
            return
            
        # Check if SMTP is configured
        if "smtp" not in self.config or not self.config["smtp"].get("server"):
            logger.warning("SMTP not configured")
            return
            
        # Create email content
        subject = "FEMA Policy Updates - " + datetime.now().strftime("%Y-%m-%d")
        
        # Create HTML content
        html_content = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .update {{ margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; }}
                .new {{ background-color: #e6ffe6; }}
                .changed {{ background-color: #e6f0ff; }}
                h2 {{ color: #333366; }}
            </style>
        </head>
        <body>
            <h1>FEMA Policy Updates</h1>
            <p>The following policy updates were detected:</p>
        """
        
        # Add updates to content
        for source_id, source_updates in updates.items():
            if not source_updates:
                continue
                
            source_name = source_updates[0].get("source_name", source_id)
            
            html_content += f"<h2>{source_name}</h2>"
            
            for update in source_updates:
                update_type = update.get("type", "new")
                css_class = "update " + update_type
                
                html_content += f"""
                <div class="{css_class}">
                    <h3>{update.get('title', 'Untitled')}</h3>
                    <p><strong>Type:</strong> {update_type}</p>
                    <p><strong>URL:</strong> <a href="{update.get('url', '#')}">{update.get('url', 'N/A')}</a></p>
                    <p><strong>Detected:</strong> {update.get('timestamp', 'N/A')}</p>
                </div>
                """
                
        html_content += """
            <p>This is an automated notification from the ComplianceMax system.</p>
        </body>
        </html>
        """
        
        # Create plain text content
        text_content = "FEMA Policy Updates\n\n"
        
        for source_id, source_updates in updates.items():
            if not source_updates:
                continue
                
            source_name = source_updates[0].get("source_name", source_id)
            
            text_content += f"{source_name}\n"
            text_content += "=" * len(source_name) + "\n\n"
            
            for update in source_updates:
                text_content += f"- {update.get('title', 'Untitled')}\n"
                text_content += f"  Type: {update.get('type', 'new')}\n"
                text_content += f"  URL: {update.get('url', 'N/A')}\n"
                text_content += f"  Detected: {update.get('timestamp', 'N/A')}\n\n"
                
        text_content += "This is an automated notification from the ComplianceMax system."
        
        # Send emails to subscribers
        for subscriber in self.config["subscribers"]:
            try:
                self._send_email(
                    subscriber.get("email"),
                    subject,
                    text_content,
                    html_content
                )
                logger.info(f"Sent notification to {subscriber.get('email')}")
            except Exception as e:
                logger.error(f"Error sending notification to {subscriber.get('email')}: {str(e)}")
    
    def _send_email(self, to_email: str, subject: str, text_content: str, html_content: str):
        """Send an email"""
        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = self.config.get("from_email", "<EMAIL>")
        msg["To"] = to_email
        
        # Attach parts
        part1 = MIMEText(text_content, "plain")
        part2 = MIMEText(html_content, "html")
        
        msg.attach(part1)
        msg.attach(part2)
        
        # Connect to SMTP server
        smtp_config = self.config.get("smtp", {})
        server = smtplib.SMTP(
            smtp_config.get("server", "localhost"),
            smtp_config.get("port", 25)
        )
        
        if smtp_config.get("use_tls", False):
            server.starttls()
            
        if smtp_config.get("username") and smtp_config.get("password"):
            server.login(
                smtp_config.get("username"),
                smtp_config.get("password")
            )
            
        # Send email
        server.sendmail(
            self.config.get("from_email", "<EMAIL>"),
            to_email,
            msg.as_string()
        )
        
        server.quit()
```

### 5.3 Policy Update Scheduler

```python
# policy_update/scheduler.py
import os
import json
import time
import logging
import schedule
import threading
from datetime import datetime
from typing import Dict, Any, Optional, Callable

from .policy_monitor import FEMAPolicyMonitor
from .notification_system import PolicyUpdateNotifier

logger = logging.getLogger(__name__)

class PolicyUpdateScheduler:
    def __init__(self, config_file: str):
        """
        Initialize the policy update scheduler
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = config_file
        self.config = self._load_config()
        self.running = False
        self.thread = None
        
        # Initialize components
        cache_dir = self.config.get("cache_dir", "/tmp/policy_cache")
        self.monitor = FEMAPolicyMonitor(cache_dir)
        
        notifier_config = os.path.join(
            os.path.dirname(config_file),
            "notifier_config.json"
        )
        self.notifier = PolicyUpdateNotifier(notifier_config)
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if not os.path.exists(self.config_file):
            # Return default configuration
            return {
                "cache_dir": "/tmp/policy_cache",
                "check_interval": "daily",
                "check_time": "03:00",
                "last_check": None,
                "enabled": True
            }
            
        try:
            with open(self.config_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading config file {self.config_file}: {str(e)}")
            return {}
    
    def _save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving config file {self.config_file}: {str(e)}")
    
    def set_check_schedule(self, interval: str, time: Optional[str] = None):
        """
        Set the schedule for policy checks
        
        Args:
            interval: Interval for checks ("hourly", "daily", "weekly")
            time: Time for checks (HH:MM format, for daily and weekly)
        """
        self.config["check_interval"] = interval
        
        if time:
            self.config["check_time"] = time
            
        self._save_config()
        
        # Update schedule if running
        if self.running:
            self.stop()
            self.start()
    
    def check_for_updates(self):
        """Check for policy updates"""
        logger.info("Checking for policy updates...")
        
        try:
            # Check for updates
            updates = self.monitor.check_for_updates()
            
            # Update last check time
            self.config["last_check"] = datetime.now().isoformat()
            self._save_config()
            
            # Notify about updates
            if updates:
                logger.info(f"Found {sum(len(u) for u in updates.values())} updates")
                self.notifier.notify_updates(updates)
            else:
                logger.info("No updates found")
                
            return updates
            
        except Exception as e:
            logger.error(f"Error checking for updates: {str(e)}")
            return {}
    
    def _run_scheduler(self):
        """Run the scheduler loop"""
        while self.running:
            schedule.run_pending()
            time.sleep(1)
    
    def start(self):
        """Start the scheduler"""
        if self.running:
            return
            
        self.running = True
        
        # Clear existing schedule
        schedule.clear()
        
        # Set up schedule based on configuration
        interval = self.config.get("check_interval", "daily")
        check_time = self.config.get("check_time", "03:00")
        
        if interval == "hourly":
            schedule.every().hour.do(self.check_for_updates)
        elif interval == "daily":
            schedule.every().day.at(check_time).do(self.check_for_updates)
        elif interval == "weekly":
            schedule.every().monday.at(check_time).do(self.check_for_updates)
        else:
            logger.error(f"Unknown interval: {interval}")
            self.running = False
            return
            
        # Start scheduler thread
        self.thread = threading.Thread(target=self._run_scheduler)
        self.thread.daemon = True
        self.thread.start()
        
        logger.info(f"Scheduler started with {interval} checks")
    
    def stop(self):
        """Stop the scheduler"""
        self.running = False
        
        if self.thread:
            self.thread.join(timeout=5)
            self.thread = None
            
        logger.info("Scheduler stopped")
```

## 6. Integration with ComplianceMax System

### 6.1 System Integration Architecture

To integrate these components with the ComplianceMax system, we recommend the following architecture:

1. **Core Validation Engine**: Implement the validation engine as a standalone service that can be called by the main application.

2. **API Endpoints**: Create RESTful API endpoints for:
   - Validation requests
   - Report generation
   - Audit log queries
   - Evidence snapshot creation and retrieval
   - Policy update status

3. **Database Schema Updates**: Add tables for:
   - Validation rules
   - Validation results
   - Audit logs
   - Evidence snapshots
   - User action traces
   - Policy updates

4. **Background Jobs**: Implement scheduled jobs for:
   - Policy update checks
   - Validation result aggregation
   - Report generation

### 6.2 Implementation Roadmap

1. **Phase 1: Core Validation Engine**
   - Implement rule registry and validation engine
   - Create initial rule set based on FEMA PA policies
   - Integrate with existing project data

2. **Phase 2: Reporting and Audit Features**
   - Implement compliance dashboard
   - Develop PDF report generator
   - Add audit logging and evidence snapshots

3. **Phase 3: Policy Update Mechanism**
   - Implement policy monitor
   - Create notification system
   - Set up scheduled checks

4. **Phase 4: System Integration**
   - Connect components to main application
   - Update UI to display compliance status
   - Add user permissions for compliance features

## 7. References

1. FEMA Public Assistance Program and Policy Guide (PAPPG) Version 5.0: https://www.fema.gov/press-release/20250107/fema-publishes-updated-public-assistance-guide-enhance-disaster-recovery

2. 44 CFR Part 206 - Federal Disaster Assistance: https://www.ecfr.gov/current/title-44/chapter-I/subchapter-D/part-206

3. FEMA Public Assistance Simplified Procedures Policy: https://www.fema.gov/sites/default/files/documents/fema_pa-simplified-procedures-policy.pdf

4. FEMA "Validate As You Go" Guide: https://www.fema.gov/sites/default/files/documents/fema_pa-validate-as-you-go-guide.pdf

5. FEMA Public Assistance Sampling Procedure: https://www.fema.gov/sites/default/files/documents/fema_public-assistance-sampling-procedure.pdf

6. FEMA Audit Readiness Guidance: https://www.fema.gov/sites/default/files/documents/fema_audit-related-guidance-entities-receiving_public-assistance_4-6-2021.pdf
