# Boilerplate Generation API

Endpoint: `POST /api/intake/professional/generate-boilerplate`

Generates professional-grade technical boilerplate sections for a list of CBCS codes and returns an executive summary plus meta-data.

## Authentication
Currently **no authentication token** is required (subject to change when auth middleware is enabled).

## Request Body (application/json)
```json
{
  "selectedCodes": [
    "ASCE_7_16",
    "AASHTO_LRFD_Bridge"
  ],
  "projectDetails": {
    "project_name": "Example Bridge",
    "estimated_cost": 2500000,
    "location": "TX"
  }
}
```
• `selectedCodes` – list of CBCS/standard identifiers (strings).  
• `projectDetails` – optional free-form object passed through for future analytics.

## Successful Response — HTTP 200
```json
{
  "status": "success",
  "boilerplate_sections": [
    {
      "code": "ASCE/SEI 7-16 Minimum Design Loads for Buildings and Other Structures",
      "professional_analysis": "\n**STRUCTURAL LOAD ANALYSIS:**\n• Risk Category classification per Table 1.5-1 determines...",
      "mandatory_provisions": [
        "Risk Category determination and importance factors",
        "Site-specific seismic design parameters",
        "Wind tunnel testing for complex geometries"
      ],
      "cost_implications": "ASCE 7 compliance adds 3-5% to structural costs but ensures code compliance nationwide"
    },
    {
      "code": "AASHTO LRFD Bridge Design Specifications",
      "professional_analysis": "\n**TECHNICAL COMPLIANCE ANALYSIS:**\n• Load Factor Design methodology per AASHTO LRFD 8th Edition...",
      "mandatory_provisions": [
        "Load and Resistance Factor Design (LRFD) methodology",
        "Service Level design load combinations",
        "Seismic design per Extreme Event loading"
      ],
      "cost_implications": "LRFD design typically adds 8-12% to structural costs but reduces long-term maintenance by 25-30%"
    }
  ],
  "executive_summary": "\n**CBCS COMPLIANCE EXECUTIVE SUMMARY**\nProject requires adherence to 2 consensus-based codes and standards per DRRA Section 1235b...",
  "professional_notes": {
    "compliance_level": "Full DRRA Section 1235b compliance achieved",
    "review_required": "Professional engineer review and stamping required",
    "timeline_impact": "2-4 week extension for technical analysis",
    "cost_impact": "5-15% increase over baseline construction costs"
  },
  "generated_for": 2,
  "timestamp": "2025-06-14T10:15:32.123Z"
}
```

## Error Responses
| HTTP Code | Reason                              | Example Message                       |
|-----------|-------------------------------------|---------------------------------------|
| 400       | No codes submitted / bad JSON       | `{ "status": "error", "message": "No codes selected for boilerplate generation" }` |
| 500       | Internal error during generation    | `{ "status": "error", "message": "Boilerplate generation failed" }` |

## Notes
* The `professional_analysis` field is rich-text (Markdown) and can be rendered directly in a preview component.
* The endpoint is stateless; submissions are *not* stored unless upstream logging is enabled.
* Typical generation time is < 150 ms in local tests. 