# ComplianceMax System Enhancement: Feature Additions Implementation Plan

## Executive Summary

This document outlines a comprehensive implementation plan for enhancing the ComplianceMax system with new features that will significantly increase its value to users managing FEMA Public Assistance program compliance. The plan addresses five key enhancement areas:

1. Advanced analytics and reporting capabilities
2. Workflow enhancements for compliance processes
3. Integration capabilities with other systems
4. Mobile features for field operations
5. AI-powered automation for compliance tasks

Each section includes technical specifications, architecture decisions, implementation details, and code examples to guide the development team.

## 1. Advanced Analytics and Reporting Capabilities

### 1.1 Overview

The advanced analytics module will transform ComplianceMax from a compliance tracking system into a strategic decision-making platform by providing real-time insights, predictive analytics, and customizable reporting.

### 1.2 Technical Architecture

#### Data Warehouse Implementation

```python
# data_warehouse_schema.py
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class FactCompliance(Base):
    __tablename__ = 'fact_compliance'
    
    id = Column(Integer, primary_key=True)
    project_id = Column(Integer, ForeignKey('dim_project.id'))
    document_id = Column(Integer, ForeignKey('dim_document.id'))
    requirement_id = Column(Integer, ForeignKey('dim_requirement.id'))
    date_id = Column(Integer, ForeignKey('dim_date.id'))
    status_id = Column(Integer, ForeignKey('dim_status.id'))
    compliance_score = Column(Float)
    processing_time = Column(Integer)  # in minutes
    review_count = Column(Integer)
    rejection_count = Column(Integer)
    
class DimProject(Base):
    __tablename__ = 'dim_project'
    
    id = Column(Integer, primary_key=True)
    project_name = Column(String(255))
    project_type = Column(String(100))
    disaster_id = Column(String(50))
    region = Column(String(100))
    total_funding = Column(Float)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    
# Additional dimension tables for documents, requirements, dates, status, etc.
```

#### ETL Pipeline Implementation

```python
# etl_pipeline.py
import pandas as pd
from sqlalchemy import create_engine
from datetime import datetime, timedelta

class ComplianceETL:
    def __init__(self, source_conn, warehouse_conn):
        self.source_engine = create_engine(source_conn)
        self.warehouse_engine = create_engine(warehouse_conn)
        
    def extract_compliance_data(self, start_date, end_date):
        query = """
        SELECT p.id as project_id, p.name as project_name, p.type as project_type,
               p.disaster_id, p.region, p.total_funding, p.start_date, p.end_date,
               d.id as document_id, d.name as document_name, d.type as document_type,
               r.id as requirement_id, r.name as requirement_name, r.category,
               c.status, c.compliance_score, c.processing_time, c.review_count,
               c.rejection_count, c.created_at, c.updated_at
        FROM compliance c
        JOIN projects p ON c.project_id = p.id
        JOIN documents d ON c.document_id = d.id
        JOIN requirements r ON c.requirement_id = r.id
        WHERE c.updated_at BETWEEN :start_date AND :end_date
        """
        
        return pd.read_sql(query, self.source_engine, 
                          params={"start_date": start_date, "end_date": end_date})
    
    def transform_data(self, df):
        # Create date dimension entries
        date_df = pd.DataFrame({
            'date': pd.to_datetime(df['updated_at']).dt.date.unique()
        })
        date_df['year'] = date_df['date'].dt.year
        date_df['month'] = date_df['date'].dt.month
        date_df['day'] = date_df['date'].dt.day
        date_df['quarter'] = date_df['date'].dt.quarter
        date_df['day_of_week'] = date_df['date'].dt.dayofweek
        
        # Transform other dimensions and facts similarly
        # ...
        
        return transformed_data_dict
    
    def load_data(self, data_dict):
        for table_name, df in data_dict.items():
            df.to_sql(table_name, self.warehouse_engine, if_exists='append', index=False)
    
    def run_pipeline(self, days_back=1):
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        raw_data = self.extract_compliance_data(start_date, end_date)
        transformed_data = self.transform_data(raw_data)
        self.load_data(transformed_data)
```

### 1.3 Interactive Dashboard Implementation

```javascript
// dashboard.js (React component)
import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, DatePicker, Button } from 'antd';
import { Line, Bar, Pie, Scatter } from 'react-chartjs-2';
import axios from 'axios';

const { RangePicker } = DatePicker;
const { Option } = Select;

const ComplianceDashboard = () => {
  const [timeRange, setTimeRange] = useState([moment().subtract(30, 'days'), moment()]);
  const [region, setRegion] = useState('all');
  const [projectType, setProjectType] = useState('all');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    fetchDashboardData();
  }, [timeRange, region, projectType]);
  
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/analytics/dashboard', {
        params: {
          startDate: timeRange[0].format('YYYY-MM-DD'),
          endDate: timeRange[1].format('YYYY-MM-DD'),
          region: region,
          projectType: projectType
        }
      });
      setDashboardData(response.data);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="dashboard-container">
      <div className="dashboard-filters">
        <Row gutter={16}>
          <Col span={8}>
            <RangePicker 
              value={timeRange}
              onChange={setTimeRange}
            />
          </Col>
          <Col span={6}>
            <Select 
              placeholder="Select Region"
              style={{ width: '100%' }}
              value={region}
              onChange={setRegion}
            >
              <Option value="all">All Regions</Option>
              <Option value="northeast">Northeast</Option>
              <Option value="southeast">Southeast</Option>
              {/* More regions */}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="Project Type"
              style={{ width: '100%' }}
              value={projectType}
              onChange={setProjectType}
            >
              <Option value="all">All Types</Option>
              <Option value="emergency">Emergency Work</Option>
              <Option value="permanent">Permanent Work</Option>
              {/* More project types */}
            </Select>
          </Col>
          <Col span={4}>
            <Button type="primary" onClick={fetchDashboardData}>
              Refresh
            </Button>
          </Col>
        </Row>
      </div>
      
      <div className="dashboard-content">
        <Row gutter={16}>
          <Col span={8}>
            <Card title="Compliance Score Trend" loading={loading}>
              {dashboardData && (
                <Line 
                  data={dashboardData.complianceScoreTrend}
                  options={{
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
              )}
            </Card>
          </Col>
          <Col span={8}>
            <Card title="Processing Time by Document Type" loading={loading}>
              {dashboardData && (
                <Bar 
                  data={dashboardData.processingTimeByDocType}
                />
              )}
            </Card>
          </Col>
          <Col span={8}>
            <Card title="Compliance Status Distribution" loading={loading}>
              {dashboardData && (
                <Pie 
                  data={dashboardData.complianceStatusDistribution}
                />
              )}
            </Card>
          </Col>
        </Row>
        
        {/* Additional dashboard components */}
      </div>
    </div>
  );
};

export default ComplianceDashboard;
```

### 1.4 Predictive Analytics Implementation

```python
# predictive_analytics.py
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline

class CompliancePredictiveModel:
    def __init__(self):
        self.model = None
        self.preprocessor = None
        
    def prepare_data(self, df):
        # Define features and target
        X = df.drop(['compliance_score', 'id'], axis=1)
        y = df['compliance_score']
        
        # Define categorical and numerical features
        categorical_features = ['project_type', 'region', 'document_type', 'requirement_category']
        numerical_features = ['total_funding', 'processing_time', 'review_count', 'days_to_deadline']
        
        # Create preprocessor
        self.preprocessor = ColumnTransformer(
            transformers=[
                ('num', StandardScaler(), numerical_features),
                ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
            ])
        
        return train_test_split(X, y, test_size=0.2, random_state=42)
    
    def train_model(self, df):
        X_train, X_test, y_train, y_test = self.prepare_data(df)
        
        # Create and train the model
        self.model = Pipeline([
            ('preprocessor', self.preprocessor),
            ('regressor', RandomForestRegressor(n_estimators=100, random_state=42))
        ])
        
        self.model.fit(X_train, y_train)
        
        # Evaluate the model
        train_score = self.model.score(X_train, y_train)
        test_score = self.model.score(X_test, y_test)
        
        return {
            'train_score': train_score,
            'test_score': test_score,
            'feature_importance': self.get_feature_importance()
        }
    
    def predict_compliance(self, new_data):
        if self.model is None:
            raise ValueError("Model not trained yet")
        
        return self.model.predict(new_data)
    
    def get_feature_importance(self):
        if self.model is None:
            raise ValueError("Model not trained yet")
            
        feature_names = (self.preprocessor.named_transformers_['num'].get_feature_names_out().tolist() + 
                         self.preprocessor.named_transformers_['cat'].get_feature_names_out().tolist())
        
        importances = self.model.named_steps['regressor'].feature_importances_
        
        return dict(zip(feature_names, importances))
```

### 1.5 Custom Report Generator

```python
# report_generator.py
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from io import BytesIO
import base64
from jinja2 import Template
import pdfkit

class ComplianceReportGenerator:
    def __init__(self, db_connection):
        self.conn = db_connection
        
    def generate_report(self, report_config):
        """
        Generate a custom compliance report based on the provided configuration.
        
        Args:
            report_config (dict): Configuration for the report including:
                - title: Report title
                - description: Report description
                - time_period: Time period for the report
                - filters: Filters to apply to the data
                - sections: List of report sections to include
                - format: Output format (pdf, excel, html)
                
        Returns:
            bytes: The generated report in the requested format
        """
        # Fetch data based on config
        data = self._fetch_report_data(report_config)
        
        # Generate visualizations
        visualizations = self._generate_visualizations(data, report_config)
        
        # Compile report
        if report_config['format'] == 'pdf':
            return self._compile_pdf_report(data, visualizations, report_config)
        elif report_config['format'] == 'excel':
            return self._compile_excel_report(data, report_config)
        elif report_config['format'] == 'html':
            return self._compile_html_report(data, visualizations, report_config)
        else:
            raise ValueError(f"Unsupported report format: {report_config['format']}")
    
    def _fetch_report_data(self, config):
        # Build SQL query based on config
        base_query = """
        SELECT 
            p.name as project_name, 
            p.type as project_type,
            p.region,
            p.disaster_id,
            d.name as document_name,
            d.type as document_type,
            r.name as requirement_name,
            r.category as requirement_category,
            c.status,
            c.compliance_score,
            c.processing_time,
            c.review_count,
            c.created_at,
            c.updated_at
        FROM fact_compliance fc
        JOIN dim_project p ON fc.project_id = p.id
        JOIN dim_document d ON fc.document_id = d.id
        JOIN dim_requirement r ON fc.requirement_id = r.id
        JOIN dim_status s ON fc.status_id = s.id
        JOIN dim_date dt ON fc.date_id = dt.id
        WHERE dt.date BETWEEN :start_date AND :end_date
        """
        
        # Add filters
        filter_conditions = []
        params = {
            'start_date': config['time_period']['start'],
            'end_date': config['time_period']['end']
        }
        
        for key, value in config['filters'].items():
            if value:
                filter_conditions.append(f"{key} = :{key}")
                params[key] = value
                
        if filter_conditions:
            base_query += " AND " + " AND ".join(filter_conditions)
            
        # Execute query
        return pd.read_sql(base_query, self.conn, params=params)
    
    def _generate_visualizations(self, data, config):
        visualizations = {}
        
        for section in config['sections']:
            if section['type'] == 'compliance_trend':
                fig, ax = plt.subplots(figsize=(10, 6))
                trend_data = data.groupby(pd.Grouper(key='updated_at', freq='W'))['compliance_score'].mean()
                trend_data.plot(ax=ax)
                ax.set_title('Weekly Compliance Score Trend')
                ax.set_ylabel('Average Compliance Score')
                ax.set_xlabel('Week')
                
                img_buf = BytesIO()
                fig.savefig(img_buf, format='png')
                img_buf.seek(0)
                img_data = base64.b64encode(img_buf.read()).decode('utf-8')
                visualizations['compliance_trend'] = img_data
                
            # Additional visualization types...
                
        return visualizations
    
    def _compile_html_report(self, data, visualizations, config):
        template_str = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{{ config.title }}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #2c3e50; }
                .section { margin-bottom: 30px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .visualization { margin: 20px 0; text-align: center; }
            </style>
        </head>
        <body>
            <h1>{{ config.title }}</h1>
            <p>{{ config.description }}</p>
            <p><strong>Period:</strong> {{ config.time_period.start }} to {{ config.time_period.end }}</p>
            
            {% for section in config.sections %}
            <div class="section">
                <h2>{{ section.title }}</h2>
                
                {% if section.type == 'compliance_trend' and 'compliance_trend' in visualizations %}
                <div class="visualization">
                    <img src="data:image/png;base64,{{ visualizations.compliance_trend }}" alt="Compliance Trend">
                </div>
                {% endif %}
                
                {% if section.type == 'data_table' %}
                <table>
                    <thead>
                        <tr>
                            {% for col in section.columns %}
                            <th>{{ col }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for _, row in data[section.columns].iterrows() %}
                        <tr>
                            {% for col in section.columns %}
                            <td>{{ row[col] }}</td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% endif %}
                
                <!-- Additional section types -->
            </div>
            {% endfor %}
        </body>
        </html>
        """
        
        template = Template(template_str)
        html_content = template.render(
            config=config,
            data=data,
            visualizations=visualizations
        )
        
        return html_content
    
    def _compile_pdf_report(self, data, visualizations, config):
        html_content = self._compile_html_report(data, visualizations, config)
        pdf_content = pdfkit.from_string(html_content, False)
        return pdf_content
    
    def _compile_excel_report(self, data, config):
        output = BytesIO()
        
        with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
            # Summary sheet
            summary_data = pd.DataFrame({
                'Metric': ['Total Projects', 'Average Compliance Score', 'Processing Time (avg)'],
                'Value': [
                    data['project_name'].nunique(),
                    data['compliance_score'].mean(),
                    data['processing_time'].mean()
                ]
            })
            summary_data.to_excel(writer, sheet_name='Summary', index=False)
            
            # Data sheets for each section
            for section in config['sections']:
                if section['type'] == 'data_table':
                    section_data = data[section['columns']]
                    section_data.to_excel(writer, sheet_name=section['title'][:31], index=False)
        
        output.seek(0)
        return output.getvalue()
```

### 1.6 Implementation Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| Planning & Design | 3 weeks | Finalize data warehouse schema, dashboard mockups, report templates |
| Data Warehouse Setup | 2 weeks | Set up data warehouse infrastructure, implement ETL pipelines |
| Dashboard Development | 4 weeks | Develop interactive dashboards, implement filters and visualizations |
| Predictive Analytics | 3 weeks | Develop and train predictive models, implement API endpoints |
| Report Generator | 3 weeks | Implement custom report generator, create report templates |
| Testing & QA | 2 weeks | Comprehensive testing of all analytics features |
| Deployment | 1 week | Deploy to production environment |
| Training & Documentation | 2 weeks | Create user documentation, conduct training sessions |

## 2. Workflow Enhancements for Compliance Processes

### 2.1 Overview

The workflow enhancement module will streamline compliance processes by implementing a flexible, rule-based workflow engine that can adapt to changing FEMA requirements and organizational needs.

### 2.2 Technical Architecture

#### Workflow Engine Implementation

```python
# workflow_engine.py
from enum import Enum
import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class WorkflowStatus(Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    ARCHIVED = "archived"

class StepStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"
    SKIPPED = "skipped"

class WorkflowDefinition(Base):
    __tablename__ = 'workflow_definitions'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    version = Column(String(50), nullable=False)
    status = Column(String(50), nullable=False)
    definition = Column(Text, nullable=False)  # JSON schema of the workflow
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    instances = relationship("WorkflowInstance", back_populates="definition")
    
    def get_definition_schema(self):
        return json.loads(self.definition)

class WorkflowInstance(Base):
    __tablename__ = 'workflow_instances'
    
    id = Column(Integer, primary_key=True)
    definition_id = Column(Integer, ForeignKey('workflow_definitions.id'), nullable=False)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=False)
    current_step = Column(String(255))
    status = Column(String(50), nullable=False)
    context = Column(Text)  # JSON data for workflow context
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    definition = relationship("WorkflowDefinition", back_populates="instances")
    steps = relationship("WorkflowStep", back_populates="instance")
    
    def get_context(self):
        return json.loads(self.context) if self.context else {}
    
    def set_context(self, context_dict):
        self.context = json.dumps(context_dict)

class WorkflowStep(Base):
    __tablename__ = 'workflow_steps'
    
    id = Column(Integer, primary_key=True)
    instance_id = Column(Integer, ForeignKey('workflow_instances.id'), nullable=False)
    step_id = Column(String(255), nullable=False)
    name = Column(String(255), nullable=False)
    status = Column(String(50), nullable=False)
    assigned_to = Column(Integer, ForeignKey('users.id'))
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    data = Column(Text)  # JSON data for step inputs/outputs
    notes = Column(Text)
    
    instance = relationship("WorkflowInstance", back_populates="steps")
    
    def get_data(self):
        return json.loads(self.data) if self.data else {}
    
    def set_data(self, data_dict):
        self.data = json.dumps(data_dict)
```

#### Workflow Service Implementation

```python
# workflow_service.py
from datetime import datetime
import json
from sqlalchemy.orm import Session
from .workflow_engine import WorkflowDefinition, WorkflowInstance, WorkflowStep, WorkflowStatus, StepStatus

class WorkflowService:
    def __init__(self, db_session):
        self.session = db_session
    
    def create_workflow_definition(self, name, description, schema, version="1.0.0"):
        """Create a new workflow definition"""
        workflow_def = WorkflowDefinition(
            name=name,
            description=description,
            version=version,
            status=WorkflowStatus.DRAFT.value,
            definition=json.dumps(schema)
        )
        
        self.session.add(workflow_def)
        self.session.commit()
        return workflow_def
    
    def activate_workflow_definition(self, definition_id):
        """Activate a workflow definition"""
        workflow_def = self.session.query(WorkflowDefinition).get(definition_id)
        if not workflow_def:
            raise ValueError(f"Workflow definition {definition_id} not found")
            
        workflow_def.status = WorkflowStatus.ACTIVE.value
        self.session.commit()
        return workflow_def
    
    def start_workflow(self, definition_id, project_id, initial_context=None):
        """Start a new workflow instance"""
        workflow_def = self.session.query(WorkflowDefinition).get(definition_id)
        if not workflow_def:
            raise ValueError(f"Workflow definition {definition_id} not found")
            
        if workflow_def.status != WorkflowStatus.ACTIVE.value:
            raise ValueError(f"Cannot start workflow with inactive definition")
            
        schema = workflow_def.get_definition_schema()
        
        # Create workflow instance
        workflow_instance = WorkflowInstance(
            definition_id=definition_id,
            project_id=project_id,
            status=StepStatus.IN_PROGRESS.value,
            context=json.dumps(initial_context or {})
        )
        
        self.session.add(workflow_instance)
        self.session.flush()  # Get the instance ID
        
        # Initialize the first step
        first_step = self._get_first_step(schema)
        workflow_instance.current_step = first_step['id']
        
        # Create the first step
        step = WorkflowStep(
            instance_id=workflow_instance.id,
            step_id=first_step['id'],
            name=first_step['name'],
            status=StepStatus.PENDING.value,
            assigned_to=first_step.get('default_assignee')
        )
        
        self.session.add(step)
        self.session.commit()
        
        return workflow_instance
    
    def complete_step(self, step_id, user_id, data=None, notes=None):
        """Complete a workflow step and advance to the next step"""
        step = self.session.query(WorkflowStep).get(step_id)
        if not step:
            raise ValueError(f"Step {step_id} not found")
            
        if step.status != StepStatus.PENDING.value and step.status != StepStatus.IN_PROGRESS.value:
            raise ValueError(f"Cannot complete step with status {step.status}")
            
        # Update step data
        step.status = StepStatus.COMPLETED.value
        step.completed_at = datetime.utcnow()
        if data:
            step_data = step.get_data()
            step_data.update(data)
            step.set_data(step_data)
        if notes:
            step.notes = notes
            
        # Get workflow instance and definition
        instance = step.instance
        definition = instance.definition
        schema = definition.get_definition_schema()
        
        # Determine next step
        current_step_def = self._find_step_by_id(schema, step.step_id)
        next_step_id = self._determine_next_step(schema, current_step_def, instance.get_context(), step.get_data())
        
        if next_step_id:
            # Create next step
            next_step_def = self._find_step_by_id(schema, next_step_id)
            next_step = WorkflowStep(
                instance_id=instance.id,
                step_id=next_step_id,
                name=next_step_def['name'],
                status=StepStatus.PENDING.value,
                assigned_to=next_step_def.get('default_assignee')
            )
            
            self.session.add(next_step)
            instance.current_step = next_step_id
        else:
            # Workflow is complete
            instance.status = StepStatus.COMPLETED.value
            instance.current_step = None
            
        self.session.commit()
        return instance
    
    def reject_step(self, step_id, user_id, reason, data=None):
        """Reject a workflow step and handle the rejection logic"""
        step = self.session.query(WorkflowStep).get(step_id)
        if not step:
            raise ValueError(f"Step {step_id} not found")
            
        if step.status != StepStatus.PENDING.value and step.status != StepStatus.IN_PROGRESS.value:
            raise ValueError(f"Cannot reject step with status {step.status}")
            
        # Update step data
        step.status = StepStatus.REJECTED.value
        step.completed_at = datetime.utcnow()
        step.notes = reason
        if data:
            step_data = step.get_data()
            step_data.update(data)
            step.set_data(step_data)
            
        # Get workflow instance and definition
        instance = step.instance
        definition = instance.definition
        schema = definition.get_definition_schema()
        
        # Determine rejection path
        current_step_def = self._find_step_by_id(schema, step.step_id)
        rejection_step_id = current_step_def.get('rejection_step')
        
        if rejection_step_id:
            # Create rejection step
            rejection_step_def = self._find_step_by_id(schema, rejection_step_id)
            rejection_step = WorkflowStep(
                instance_id=instance.id,
                step_id=rejection_step_id,
                name=rejection_step_def['name'],
                status=StepStatus.PENDING.value,
                assigned_to=rejection_step_def.get('default_assignee')
            )
            
            self.session.add(rejection_step)
            instance.current_step = rejection_step_id
        else:
            # No rejection path defined, workflow is rejected
            instance.status = StepStatus.REJECTED.value
            
        self.session.commit()
        return instance
    
    def _get_first_step(self, schema):
        """Get the first step in a workflow schema"""
        if 'steps' not in schema or not schema['steps']:
            raise ValueError("Workflow schema must contain steps")
            
        for step in schema['steps']:
            if step.get('is_start', False):
                return step
                
        # If no explicit start step, use the first one
        return schema['steps'][0]
    
    def _find_step_by_id(self, schema, step_id):
        """Find a step in the schema by ID"""
        for step in schema['steps']:
            if step['id'] == step_id:
                return step
        return None
    
    def _determine_next_step(self, schema, current_step, context, step_data):
        """Determine the next step based on transitions and conditions"""
        if 'transitions' not in current_step:
            return None
            
        for transition in current_step['transitions']:
            if 'condition' in transition:
                # Evaluate condition
                condition_met = self._evaluate_condition(transition['condition'], context, step_data)
                if condition_met:
                    return transition['target']
            else:
                # Unconditional transition
                return transition['target']
                
        return None
    
    def _evaluate_condition(self, condition, context, step_data):
        """Evaluate a condition expression"""
        # Simple implementation - in production, use a proper expression evaluator
        # This is just a placeholder for demonstration
        if condition == "always":
            return True
            
        if condition.startswith("data."):
            field = condition.split('.')[1].split('==')[0].strip()
            value = condition.split('==')[1].strip().strip('"\'')
            return step_data.get(field) == value
            
        if condition.startswith("context."):
            field = condition.split('.')[1].split('==')[0].strip()
            value = condition.split('==')[1].strip().strip('"\'')
            return context.get(field) == value
            
        return False
```

### 2.3 Workflow Designer UI Implementation

```javascript
// WorkflowDesigner.jsx (React component)
import React, { useState, useCallback } from 'react';
import ReactFlow, { 
  addEdge, 
  Background, 
  Controls, 
  MiniMap 
} from 'react-flow-renderer';
import { 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Card, 
  Tabs, 
  message 
} from 'antd';
import axios from 'axios';

const { Option } = Select;
const { TabPane } = Tabs;

// Custom node types
const nodeTypes = {
  taskNode: TaskNode,
  decisionNode: DecisionNode,
  startNode: StartNode,
  endNode: EndNode,
};

const WorkflowDesigner = () => {
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [isNodeModalVisible, setIsNodeModalVisible] = useState(false);
  const [isEdgeModalVisible, setIsEdgeModalVisible] = useState(false);
  const [selectedEdge, setSelectedEdge] = useState(null);
  const [workflowName, setWorkflowName] = useState('');
  const [workflowDescription, setWorkflowDescription] = useState('');
  
  const onConnect = useCallback((params) => {
    setSelectedEdge(params);
    setIsEdgeModalVisible(true);
  }, []);
  
  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
    setIsNodeModalVisible(true);
  }, []);
  
  const onAddNode = (type) => {
    const newNode = {
      id: `node_${Date.now()}`,
      type: type,
      position: { x: 250, y: 250 },
      data: { 
        label: `New ${type.replace('Node', '')}`,
        properties: {}
      }
    };
    
    setNodes((nds) => nds.concat(newNode));
    setSelectedNode(newNode);
    setIsNodeModalVisible(true);
  };
  
  const onSaveNode = (formValues) => {
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === selectedNode.id) {
          return {
            ...node,
            data: {
              ...node.data,
              label: formValues.name,
              properties: formValues
            }
          };
        }
        return node;
      })
    );
    
    setIsNodeModalVisible(false);
  };
  
  const onSaveEdge = (formValues) => {
    const newEdge = {
      ...selectedEdge,
      id: `edge_${Date.now()}`,
      label: formValues.condition || '',
      data: { condition: formValues.condition }
    };
    
    setEdges((eds) => addEdge(newEdge, eds));
    setIsEdgeModalVisible(false);
  };
  
  const onSaveWorkflow = async () => {
    try {
      // Convert the visual workflow to the schema format
      const workflowSchema = convertToSchema(nodes, edges, workflowName, workflowDescription);
      
      // Save the workflow definition
      const response = await axios.post('/api/workflows/definitions', {
        name: workflowName,
        description: workflowDescription,
        schema: workflowSchema
      });
      
      message.success('Workflow saved successfully');
      return response.data;
    } catch (error) {
      message.error('Failed to save workflow');
      console.error(error);
    }
  };
  
  const convertToSchema = (nodes, edges, name, description) => {
    // Convert the visual representation to the workflow schema format
    const steps = nodes.map(node => {
      const baseStep = {
        id: node.id,
        name: node.data.label,
        type: node.type.replace('Node', ''),
        ...node.data.properties
      };
      
      if (node.type === 'startNode') {
        baseStep.is_start = true;
      }
      
      // Add transitions based on edges
      const outgoingEdges = edges.filter(edge => edge.source === node.id);
      if (outgoingEdges.length > 0) {
        baseStep.transitions = outgoingEdges.map(edge => ({
          target: edge.target,
          condition: edge.data?.condition || 'always'
        }));
      }
      
      return baseStep;
    });
    
    return {
      name,
      description,
      version: '1.0.0',
      steps
    };
  };
  
  return (
    <div style={{ height: '80vh', width: '100%' }}>
      <div style={{ padding: '10px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Input
            placeholder="Workflow Name"
            value={workflowName}
            onChange={(e) => setWorkflowName(e.target.value)}
            style={{ width: 200, marginRight: 10 }}
          />
          <Input
            placeholder="Description"
            value={workflowDescription}
            onChange={(e) => setWorkflowDescription(e.target.value)}
            style={{ width: 300 }}
          />
        </div>
        <div>
          <Button onClick={() => onAddNode('startNode')} style={{ marginRight: 5 }}>Add Start</Button>
          <Button onClick={() => onAddNode('taskNode')} style={{ marginRight: 5 }}>Add Task</Button>
          <Button onClick={() => onAddNode('decisionNode')} style={{ marginRight: 5 }}>Add Decision</Button>
          <Button onClick={() => onAddNode('endNode')} style={{ marginRight: 5 }}>Add End</Button>
          <Button type="primary" onClick={onSaveWorkflow}>Save Workflow</Button>
        </div>
      </div>
      
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        fitView
      >
        <Controls />
        <MiniMap />
        <Background variant="dots" gap={12} size={1} />
      </ReactFlow>
      
      {/* Node Configuration Modal */}
      <Modal
        title="Configure Node"
        visible={isNodeModalVisible}
        onCancel={() => setIsNodeModalVisible(false)}
        footer={null}
      >
        {selectedNode && (
          <NodeConfigForm
            node={selectedNode}
            onSave={onSaveNode}
            onCancel={() => setIsNodeModalVisible(false)}
          />
        )}
      </Modal>
      
      {/* Edge Configuration Modal */}
      <Modal
        title="Configure Transition"
        visible={isEdgeModalVisible}
        onCancel={() => setIsEdgeModalVisible(false)}
        footer={null}
      >
        {selectedEdge && (
          <EdgeConfigForm
            edge={selectedEdge}
            onSave={onSaveEdge}
            onCancel={() => setIsEdgeModalVisible(false)}
          />
        )}
      </Modal>
    </div>
  );
};

// Node configuration form component
const NodeConfigForm = ({ node, onSave, onCancel }) => {
  const [form] = Form.useForm();
  
  const onFinish = (values) => {
    onSave(values);
  };
  
  React.useEffect(() => {
    form.setFieldsValue({
      name: node.data.label,
      ...node.data.properties
    });
  }, [node, form]);
  
  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item name="name" label="Name" rules={[{ required: true }]}>
        <Input />
      </Form.Item>
      
      {node.type === 'taskNode' && (
        <>
          <Form.Item name="assignee_type" label="Assignee Type">
            <Select>
              <Option value="role">Role</Option>
              <Option value="user">Specific User</Option>
              <Option value="dynamic">Dynamic Assignment</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="assignee_value" label="Assignee Value">
            <Input />
          </Form.Item>
          
          <Form.Item name="form_template" label="Form Template">
            <Select>
              <Option value="document_upload">Document Upload</Option>
              <Option value="approval">Approval Form</Option>
              <Option value="review">Review Form</Option>
              <Option value="custom">Custom Form</Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="sla_days" label="SLA (Days)">
            <Input type="number" />
          </Form.Item>
        </>
      )}
      
      {node.type === 'decisionNode' && (
        <Form.Item name="decision_type" label="Decision Type">
          <Select>
            <Option value="manual">Manual Decision</Option>
            <Option value="automatic">Automatic Rule-Based</Option>
            <Option value="data_driven">Data-Driven</Option>
          </Select>
        </Form.Item>
      )}
      
      <Form.Item>
        <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
          Save
        </Button>
        <Button onClick={onCancel}>Cancel</Button>
      </Form.Item>
    </Form>
  );
};

// Edge configuration form component
const EdgeConfigForm = ({ edge, onSave, onCancel }) => {
  const [form] = Form.useForm();
  
  const onFinish = (values) => {
    onSave(values);
  };
  
  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item name="condition" label="Condition">
        <Input placeholder="e.g., data.status == 'approved' or always" />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
          Save
        </Button>
        <Button onClick={onCancel}>Cancel</Button>
      </Form.Item>
    </Form>
  );
};

export default WorkflowDesigner;
```

### 2.4 Task Management Implementation

```python
# task_service.py
from datetime import datetime, timedelta
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session

Base = declarative_base()

class TaskPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"
    DEFERRED = "deferred"

class Task(Base):
    __tablename__ = 'tasks'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(50), nullable=False)
    priority = Column(String(50), nullable=False)
    assigned_to = Column(Integer, ForeignKey('users.id'))
    assigned_by = Column(Integer, ForeignKey('users.id'))
    project_id = Column(Integer, ForeignKey('projects.id'))
    workflow_step_id = Column(Integer, ForeignKey('workflow_steps.id'))
    due_date = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # Relationships
    assignee = relationship("User", foreign_keys=[assigned_to])
    assigner = relationship("User", foreign_keys=[assigned_by])
    project = relationship("Project")
    workflow_step = relationship("WorkflowStep")
    comments = relationship("TaskComment", back_populates="task")
    
class TaskComment(Base):
    __tablename__ = 'task_comments'
    
    id = Column(Integer, primary_key=True)
    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    comment = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    task = relationship("Task", back_populates="comments")
    user = relationship("User")

class TaskService:
    def __init__(self, db_session):
        self.session = db_session
    
    def create_task(self, title, description, assigned_to, project_id, 
                   priority=TaskPriority.MEDIUM.value, due_date=None, 
                   workflow_step_id=None, assigned_by=None):
        """Create a new task"""
        task = Task(
            title=title,
            description=description,
            status=TaskStatus.PENDING.value,
            priority=priority,
            assigned_to=assigned_to,
            assigned_by=assigned_by,
            project_id=project_id,
            workflow_step_id=workflow_step_id,
            due_date=due_date
        )
        
        self.session.add(task)
        self.session.commit()
        return task
    
    def update_task_status(self, task_id, status, user_id, comment=None):
        """Update a task's status"""
        task = self.session.query(Task).get(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")
            
        old_status = task.status
        task.status = status
        
        if status == TaskStatus.COMPLETED.value:
            task.completed_at = datetime.utcnow()
            
        self.session.commit()
        
        # Add a comment if provided
        if comment:
            self.add_comment(task_id, user_id, comment)
            
        # If this is a workflow task, update the workflow step
        if task.workflow_step_id:
            workflow_service = WorkflowService(self.session)
            if status == TaskStatus.COMPLETED.value:
                workflow_service.complete_step(task.workflow_step_id, user_id)
            elif status == TaskStatus.REJECTED.value:
                workflow_service.reject_step(task.workflow_step_id, user_id, comment or "Task rejected")
                
        return task
    
    def assign_task(self, task_id, assigned_to, assigned_by):
        """Assign a task to a user"""
        task = self.session.query(Task).get(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")
            
        task.assigned_to = assigned_to
        task.assigned_by = assigned_by
        
        self.session.commit()
        return task
    
    def add_comment(self, task_id, user_id, comment):
        """Add a comment to a task"""
        task_comment = TaskComment(
            task_id=task_id,
            user_id=user_id,
            comment=comment
        )
        
        self.session.add(task_comment)
        self.session.commit()
        return task_comment
    
    def get_user_tasks(self, user_id, status=None, priority=None, project_id=None):
        """Get tasks assigned to a user with optional filters"""
        query = self.session.query(Task).filter(Task.assigned_to == user_id)
        
        if status:
            query = query.filter(Task.status == status)
        if priority:
            query = query.filter(Task.priority == priority)
        if project_id:
            query = query.filter(Task.project_id == project_id)
            
        return query.order_by(Task.due_date).all()
    
    def get_overdue_tasks(self):
        """Get all overdue tasks"""
        now = datetime.utcnow()
        return self.session.query(Task).filter(
            Task.due_date < now,
            Task.status != TaskStatus.COMPLETED.value,
            Task.status != TaskStatus.REJECTED.value
        ).order_by(Task.due_date).all()
    
    def get_tasks_due_soon(self, days=3):
        """Get tasks due within the specified number of days"""
        now = datetime.utcnow()
        soon = now + timedelta(days=days)
        return self.session.query(Task).filter(
            Task.due_date >= now,
            Task.due_date <= soon,
            Task.status != TaskStatus.COMPLETED.value,
            Task.status != TaskStatus.REJECTED.value
        ).order_by(Task.due_date).all()
```

### 2.5 Implementation Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| Planning & Design | 3 weeks | Design workflow engine architecture, define workflow schemas, design UI mockups |
| Workflow Engine Development | 4 weeks | Implement core workflow engine, database models, workflow service |
| Workflow Designer UI | 3 weeks | Develop visual workflow designer, node/edge configuration components |
| Task Management | 2 weeks | Implement task service, task assignment, notifications |
| Integration with Existing System | 3 weeks | Integrate workflow engine with existing ComplianceMax components |
| Testing & QA | 2 weeks | Comprehensive testing of workflow features |
| Deployment | 1 week | Deploy to production environment |
| Training & Documentation | 2 weeks | Create user documentation, conduct training sessions |

## 3. Integration Capabilities with Other Systems

### 3.1 Overview

The integration module will enable ComplianceMax to seamlessly connect with external systems, including FEMA's Grants Portal, document management systems, financial systems, and other compliance-related platforms.

### 3.2 Technical Architecture

#### API Gateway Implementation

```python
# api_gateway.py
from fastapi import FastAPI, Depends, HTTPException, Security, status
from fastapi.security import APIKeyHeader
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import httpx
import json
import logging
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

# Initialize FastAPI app
app = FastAPI(title="ComplianceMax API Gateway")

# API key validation
async def get_api_key(api_key_header: str = Security(api_key_header)):
    if api_key_header is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is missing"
        )
    
    # In production, validate against a secure database
    valid_api_keys = get_valid_api_keys_from_db()
    
    if api_key_header not in valid_api_keys:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return api_key_header

# Models
class IntegrationConfig(BaseModel):
    system_id: str
    name: str
    base_url: str
    auth_type: str  # "api_key", "oauth2", "basic", etc.
    auth_config: Dict[str, Any]
    endpoints: Dict[str, Dict[str, Any]]
    rate_limit: Optional[int] = None
    timeout: int = 30
    active: bool = True

class IntegrationRequest(BaseModel):
    system_id: str
    endpoint_id: str
    method: str
    params: Optional[Dict[str, Any]] = None
    data: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, Any]] = None

# In-memory cache for integration configs (in production, use Redis or similar)
integration_configs = {}

# Load integration configurations
def load_integration_configs():
    # In production, load from database
    # This is a simplified example
    configs = [
        {
            "system_id": "fema_grants_portal",
            "name": "FEMA Grants Portal",
            "base_url": "https://api.fema.gov/grants",
            "auth_type": "oauth2",
            "auth_config": {
                "client_id": "your_client_id",
                "client_secret": "your_client_secret",
                "token_url": "https://api.fema.gov/oauth/token",
                "scopes": ["read", "write"]
            },
            "endpoints": {
                "get_projects": {
                    "path": "/projects",
                    "method": "GET",
                    "params": ["disaster_id", "status", "page", "limit"],
                    "rate_limit": 100  # requests per minute
                },
                "get_project_details": {
                    "path": "/projects/{project_id}",
                    "method": "GET",
                    "rate_limit": 300
                },
                "submit_document": {
                    "path": "/projects/{project_id}/documents",
                    "method": "POST",
                    "content_type": "multipart/form-data",
                    "rate_limit": 50
                }
            },
            "rate_limit": 1000,  # global rate limit per minute
            "timeout": 30,
            "active": True
        },
        # Additional integration configurations...
    ]
    
    for config in configs:
        integration_configs[config["system_id"]] = IntegrationConfig(**config)

# Initialize configs on startup
@app.on_event("startup")
async def startup_event():
    load_integration_configs()
    logger.info("API Gateway started and integration configs loaded")

# Routes
@app.get("/integrations", dependencies=[Depends(get_api_key)])
async def list_integrations():
    """List all available integration systems"""
    return [
        {
            "system_id": config.system_id,
            "name": config.name,
            "active": config.active,
            "endpoints": list(config.endpoints.keys())
        }
        for config in integration_configs.values()
    ]

@app.get("/integrations/{system_id}", dependencies=[Depends(get_api_key)])
async def get_integration_details(system_id: str):
    """Get details of a specific integration system"""
    if system_id not in integration_configs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Integration system {system_id} not found"
        )
    
    return integration_configs[system_id]

@app.post("/integrations/request", dependencies=[Depends(get_api_key)])
async def make_integration_request(request: IntegrationRequest):
    """Make a request to an external system through the integration gateway"""
    if request.system_id not in integration_configs:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Integration system {request.system_id} not found"
        )
    
    config = integration_configs[request.system_id]
    
    if not config.active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Integration system {request.system_id} is not active"
        )
    
    if request.endpoint_id not in config.endpoints:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Endpoint {request.endpoint_id} not found for system {request.system_id}"
        )
    
    endpoint_config = config.endpoints[request.endpoint_id]
    
    # Check if method is allowed
    if request.method != endpoint_config.get("method"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Method {request.method} not allowed for endpoint {request.endpoint_id}"
        )
    
    # Prepare request URL
    path = endpoint_config["path"]
    
    # Handle path parameters
    if request.params:
        for key, value in request.params.items():
            if "{" + key + "}" in path:
                path = path.replace("{" + key + "}", str(value))
    
    url = f"{config.base_url}{path}"
    
    # Prepare headers
    headers = request.headers or {}
    
    # Add authentication
    if config.auth_type == "api_key":
        headers[config.auth_config["header_name"]] = config.auth_config["api_key"]
    elif config.auth_type == "oauth2":
        # In production, implement proper OAuth2 token management
        token = await get_oauth2_token(config.auth_config)
        headers["Authorization"] = f"Bearer {token}"
    elif config.auth_type == "basic":
        # In production, use more secure methods for storing credentials
        import base64
        auth_str = f"{config.auth_config['username']}:{config.auth_config['password']}"
        encoded = base64.b64encode(auth_str.encode()).decode()
        headers["Authorization"] = f"Basic {encoded}"
    
    # Make the request
    try:
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            if request.method == "GET":
                response = await client.get(url, params=request.params, headers=headers)
            elif request.method == "POST":
                response = await client.post(url, json=request.data, headers=headers)
            elif request.method == "PUT":
                response = await client.put(url, json=request.data, headers=headers)
            elif request.method == "DELETE":
                response = await client.delete(url, headers=headers)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Unsupported method: {request.method}"
                )
            
            # Log the request
            log_integration_request(
                system_id=request.system_id,
                endpoint_id=request.endpoint_id,
                method=request.method,
                url=url,
                status_code=response.status_code,
                response_time=response.elapsed.total_seconds()
            )
            
            # Return the response
            return {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "data": response.json() if response.headers.get("content-type") == "application/json" else response.text
            }
    except httpx.TimeoutException:
        logger.error(f"Request to {url} timed out")
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Request to external system timed out"
        )
    except httpx.RequestError as e:
        logger.error(f"Request to {url} failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail=f"Request to external system failed: {str(e)}"
        )

# Helper functions
async def get_oauth2_token(auth_config):
    """Get OAuth2 token from the authorization server"""
    # In production, implement token caching and refresh
    async with httpx.AsyncClient() as client:
        response = await client.post(
            auth_config["token_url"],
            data={
                "grant_type": "client_credentials",
                "client_id": auth_config["client_id"],
                "client_secret": auth_config["client_secret"],
                "scope": " ".join(auth_config["scopes"])
            }
        )
        
        if response.status_code != 200:
            logger.error(f"Failed to get OAuth2 token: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Failed to authenticate with external system"
            )
        
        token_data = response.json()
        return token_data["access_token"]

def log_integration_request(system_id, endpoint_id, method, url, status_code, response_time):
    """Log integration request details"""
    # In production, store in database for monitoring and analytics
    logger.info(
        f"Integration request: system={system_id}, endpoint={endpoint_id}, "
        f"method={method}, url={url}, status={status_code}, time={response_time}s"
    )

def get_valid_api_keys_from_db():
    """Get valid API keys from the database"""
    # In production, implement secure API key storage and retrieval
    return ["test_api_key_1", "test_api_key_2"]
```

#### Data Transformation Service Implementation

```python
# data_transformation_service.py
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional, Union
import json
import jsonpath_ng
import re
import csv
import io
import xml.etree.ElementTree as ET
import yaml
from datetime import datetime

class TransformationRule(BaseModel):
    source_path: str
    target_path: str
    transformation_type: str = "direct"  # direct, format, map, aggregate, custom
    format_string: Optional[str] = None
    value_map: Optional[Dict[str, Any]] = None
    aggregation_function: Optional[str] = None  # sum, avg, min, max, count
    custom_function: Optional[str] = None

class DataMapping(BaseModel):
    name: str
    description: Optional[str] = None
    source_format: str  # json, xml, csv, yaml
    target_format: str  # json, xml, csv, yaml
    rules: List[TransformationRule]
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class DataTransformationService:
    def __init__(self):
        self.mappings = {}
        self.custom_functions = {}
    
    def register_mapping(self, mapping: DataMapping):
        """Register a new data mapping"""
        self.mappings[mapping.name] = mapping
        return mapping
    
    def register_custom_function(self, name: str, func):
        """Register a custom transformation function"""
        self.custom_functions[name] = func
        return func
    
    def transform_data(self, mapping_name: str, source_data: Any) -> Any:
        """Transform data using the specified mapping"""
        if mapping_name not in self.mappings:
            raise ValueError(f"Mapping '{mapping_name}' not found")
        
        mapping = self.mappings[mapping_name]
        
        # Parse source data based on format
        parsed_source = self._parse_data(source_data, mapping.source_format)
        
        # Initialize target data structure
        target_data = {}
        
        # Apply transformation rules
        for rule in mapping.rules:
            # Extract source value
            source_value = self._extract_value(parsed_source, rule.source_path, mapping.source_format)
            
            # Apply transformation
            transformed_value = self._apply_transformation(source_value, rule)
            
            # Set target value
            self._set_value(target_data, rule.target_path, transformed_value)
        
        # Format target data based on target format
        return self._format_data(target_data, mapping.target_format)
    
    def _parse_data(self, data: Any, data_format: str) -> Any:
        """Parse data based on its format"""
        if data_format == "json":
            if isinstance(data, str):
                return json.loads(data)
            return data
        elif data_format == "xml":
            if isinstance(data, str):
                return ET.fromstring(data)
            return data
        elif data_format == "csv":
            if isinstance(data, str):
                reader = csv.DictReader(io.StringIO(data))
                return list(reader)
            return data
        elif data_format == "yaml":
            if isinstance(data, str):
                return yaml.safe_load(data)
            return data
        else:
            raise ValueError(f"Unsupported data format: {data_format}")
    
    def _extract_value(self, data: Any, path: str, data_format: str) -> Any:
        """Extract value from data using the specified path"""
        if data_format == "json":
            # Use JSONPath for JSON data
            jsonpath_expr = jsonpath_ng.parse(path)
            matches = [match.value for match in jsonpath_expr.find(data)]
            return matches[0] if matches else None
        elif data_format == "xml":
            # Use XPath for XML data
            elements = data.findall(path)
            if not elements:
                return None
            if len(elements) == 1:
                return elements[0].text
            return [elem.text for elem in elements]
        elif data_format == "csv":
            # For CSV, path is expected to be a column name
            if isinstance(data, list) and len(data) > 0:
                if path in data[0]:
                    return [row[path] for row in data]
                return None
            return None
        elif data_format == "yaml":
            # For YAML, use dot notation path
            parts = path.split('.')
            current = data
            for part in parts:
                if isinstance(current, dict) and part in current:
                    current = current[part]
                else:
                    return None
            return current
        else:
            raise ValueError(f"Unsupported data format: {data_format}")
    
    def _apply_transformation(self, value: Any, rule: TransformationRule) -> Any:
        """Apply transformation to the value based on the rule"""
        if value is None:
            return None
            
        if rule.transformation_type == "direct":
            return value
        elif rule.transformation_type == "format":
            if rule.format_string:
                if isinstance(value, list):
                    return [rule.format_string.format(v) for v in value]
                return rule.format_string.format(value)
            return value
        elif rule.transformation_type == "map":
            if rule.value_map:
                if isinstance(value, list):
                    return [rule.value_map.get(str(v), v) for v in value]
                return rule.value_map.get(str(value), value)
            return value
        elif rule.transformation_type == "aggregate":
            if not isinstance(value, list):
                return value
                
            if rule.aggregation_function == "sum":
                try:
                    return sum(float(v) for v in value)
                except (ValueError, TypeError):
                    return None
            elif rule.aggregation_function == "avg":
                try:
                    values = [float(v) for v in value]
                    return sum(values) / len(values) if values else None
                except (ValueError, TypeError):
                    return None
            elif rule.aggregation_function == "min":
                try:
                    return min(float(v) for v in value)
                except (ValueError, TypeError):
                    return None
            elif rule.aggregation_function == "max":
                try:
                    return max(float(v) for v in value)
                except (ValueError, TypeError):
                    return None
            elif rule.aggregation_function == "count":
                return len(value)
            else:
                return value
        elif rule.transformation_type == "custom":
            if rule.custom_function and rule.custom_function in self.custom_functions:
                return self.custom_functions[rule.custom_function](value)
            return value
        else:
            return value
    
    def _set_value(self, data: Dict[str, Any], path: str, value: Any):
        """Set value in data at the specified path"""
        parts = re.split(r'\.(?![^[]*\])', path)
        current = data
        
        for i, part in enumerate(parts):
            # Handle array indices in the path
            match = re.match(r'([^\[]+)(?:\[(\d+)\])?', part)
            if match:
                key, index = match.groups()
                
                if i == len(parts) - 1:
                    # Last part, set the value
                    if index is not None:
                        # Ensure the key exists and is a list
                        if key not in current or not isinstance(current[key], list):
                            current[key] = []
                            
                        # Ensure the list is long enough
                        index = int(index)
                        while len(current[key]) <= index:
                            current[key].append(None)
                            
                        current[key][index] = value
                    else:
                        current[key] = value
                else:
                    # Not the last part, navigate deeper
                    if index is not None:
                        # Ensure the key exists and is a list
                        if key not in current or not isinstance(current[key], list):
                            current[key] = []
                            
                        # Ensure the list is long enough
                        index = int(index)
                        while len(current[key]) <= index:
                            current[key].append({})
                            
                        current = current[key][index]
                    else:
                        # Ensure the key exists and is a dict
                        if key not in current or not isinstance(current[key], dict):
                            current[key] = {}
                            
                        current = current[key]
    
    def _format_data(self, data: Dict[str, Any], data_format: str) -> Any:
        """Format data based on the target format"""
        if data_format == "json":
            return json.dumps(data)
        elif data_format == "xml":
            root = ET.Element("root")
            self._dict_to_xml(root, data)
            return ET.tostring(root, encoding='unicode')
        elif data_format == "csv":
            if not isinstance(data, list):
                data = [data]
                
            output = io.StringIO()
            if data:
                writer = csv.DictWriter(output, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
                
            return output.getvalue()
        elif data_format == "yaml":
            return yaml.dump(data)
        else:
            raise ValueError(f"Unsupported data format: {data_format}")
    
    def _dict_to_xml(self, parent, data):
        """Convert a dictionary to XML elements"""
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    element = ET.SubElement(parent, key)
                    self._dict_to_xml(element, value)
                else:
                    element = ET.SubElement(parent, key)
                    element.text = str(value) if value is not None else ""
        elif isinstance(data, list):
            for item in data:
                element = ET.SubElement(parent, "item")
                self._dict_to_xml(element, item)
        else:
            parent.text = str(data) if data is not None else ""
```

#### Webhook Service Implementation

```python
# webhook_service.py
from fastapi import FastAPI, Request, HTTPException, Depends, BackgroundTasks
from fastapi.security import APIKeyHeader
from pydantic import BaseModel, Field
from typing import Dict, List, Any, Optional
import httpx
import json
import logging
import hashlib
import hmac
import time
from datetime import datetime
import uuid

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API key security
API_KEY_NAME = "X-API-Key"
api_key_header = APIKeyHeader(name=API_KEY_NAME, auto_error=False)

# Initialize FastAPI app
app = FastAPI(title="ComplianceMax Webhook Service")

# Models
class WebhookSubscription(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    event_type: str
    target_url: str
    secret: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    active: bool = True
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class WebhookEvent(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str
    payload: Dict[str, Any]
    created_at: datetime = Field(default_factory=datetime.utcnow)

class WebhookDelivery(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    webhook_id: str
    event_id: str
    status: str  # success, failed, pending
    response_code: Optional[int] = None
    response_body: Optional[str] = None
    error: Optional[str] = None
    delivery_time: Optional[float] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

# In-memory storage (in production, use a database)
webhook_subscriptions = {}
webhook_events = {}
webhook_deliveries = {}

# API key validation
async def get_api_key(api_key_header: str = Security(api_key_header)):
    if api_key_header is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="API key is missing"
        )
    
    # In production, validate against a secure database
    valid_api_keys = ["test_api_key_1", "test_api_key_2"]
    
    if api_key_header not in valid_api_keys:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return api_key_header

# Routes
@app.post("/webhooks/subscriptions", response_model=WebhookSubscription, dependencies=[Depends(get_api_key)])
async def create_webhook_subscription(subscription: WebhookSubscription):
    """Create a new webhook subscription"""
    webhook_subscriptions[subscription.id] = subscription
    logger.info(f"Created webhook subscription: {subscription.id}")
    return subscription

@app.get("/webhooks/subscriptions", response_model=List[WebhookSubscription], dependencies=[Depends(get_api_key)])
async def list_webhook_subscriptions():
    """List all webhook subscriptions"""
    return list(webhook_subscriptions.values())

@app.get("/webhooks/subscriptions/{subscription_id}", response_model=WebhookSubscription, dependencies=[Depends(get_api_key)])
async def get_webhook_subscription(subscription_id: str):
    """Get a specific webhook subscription"""
    if subscription_id not in webhook_subscriptions:
        raise HTTPException(status_code=404, detail="Webhook subscription not found")
    return webhook_subscriptions[subscription_id]

@app.delete("/webhooks/subscriptions/{subscription_id}", dependencies=[Depends(get_api_key)])
async def delete_webhook_subscription(subscription_id: str):
    """Delete a webhook subscription"""
    if subscription_id not in webhook_subscriptions:
        raise HTTPException(status_code=404, detail="Webhook subscription not found")
    
    del webhook_subscriptions[subscription_id]
    logger.info(f"Deleted webhook subscription: {subscription_id}")
    return {"message": "Webhook subscription deleted"}

@app.post("/webhooks/events", response_model=WebhookEvent, dependencies=[Depends(get_api_key)])
async def create_webhook_event(event: WebhookEvent, background_tasks: BackgroundTasks):
    """Create a new webhook event and trigger deliveries"""
    webhook_events[event.id] = event
    logger.info(f"Created webhook event: {event.id}")
    
    # Trigger webhook deliveries in the background
    background_tasks.add_task(deliver_webhook_event, event)
    
    return event

@app.get("/webhooks/events", response_model=List[WebhookEvent], dependencies=[Depends(get_api_key)])
async def list_webhook_events():
    """List all webhook events"""
    return list(webhook_events.values())

@app.get("/webhooks/deliveries", response_model=List[WebhookDelivery], dependencies=[Depends(get_api_key)])
async def list_webhook_deliveries():
    """List all webhook deliveries"""
    return list(webhook_deliveries.values())

# Webhook delivery function
async def deliver_webhook_event(event: WebhookEvent):
    """Deliver a webhook event to all matching subscriptions"""
    matching_subscriptions = [
        sub for sub in webhook_subscriptions.values()
        if sub.event_type == event.event_type and sub.active
    ]
    
    for subscription in matching_subscriptions:
        delivery_id = str(uuid.uuid4())
        
        # Create a pending delivery record
        delivery = WebhookDelivery(
            id=delivery_id,
            webhook_id=subscription.id,
            event_id=event.id,
            status="pending"
        )
        webhook_deliveries[delivery_id] = delivery
        
        try:
            # Prepare headers
            headers = subscription.headers or {}
            headers["Content-Type"] = "application/json"
            headers["X-Webhook-ID"] = subscription.id
            headers["X-Event-ID"] = event.id
            headers["X-Delivery-ID"] = delivery_id
            
            # Add signature if secret is provided
            if subscription.secret:
                payload_str = json.dumps(event.payload)
                signature = hmac.new(
                    subscription.secret.encode(),
                    payload_str.encode(),
                    hashlib.sha256
                ).hexdigest()
                headers["X-Webhook-Signature"] = signature
            
            # Send the webhook
            start_time = time.time()
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    subscription.target_url,
                    json={
                        "id": event.id,
                        "event_type": event.event_type,
                        "payload": event.payload,
                        "created_at": event.created_at.isoformat()
                    },
                    headers=headers
                )
            delivery_time = time.time() - start_time
            
            # Update delivery record
            delivery.status = "success" if response.status_code < 400 else "failed"
            delivery.response_code = response.status_code
            delivery.response_body = response.text
            delivery.delivery_time = delivery_time
            
            logger.info(
                f"Webhook delivery {delivery_id} to {subscription.target_url} "
                f"completed with status {delivery.status}"
            )
        except Exception as e:
            # Update delivery record with error
            delivery.status = "failed"
            delivery.error = str(e)
            
            logger.error(
                f"Webhook delivery {delivery_id} to {subscription.target_url} "
                f"failed with error: {str(e)}"
            )
        
        # Update the delivery record
        webhook_deliveries[delivery_id] = delivery
```

### 3.3 Integration Connectors Implementation

```python
# fema_grants_connector.py
import httpx
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio
import base64

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FEMAGrantsConnector:
    """Connector for FEMA Grants Portal API"""
    
    def __init__(self, client_id, client_secret, base_url="https://api.fema.gov/grants"):
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = base_url
        self.token = None
        self.token_expiry = None
    
    async def authenticate(self):
        """Authenticate with the FEMA Grants Portal API"""
        if self.token and self.token_expiry and datetime.now() < self.token_expiry:
            return self.token
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/oauth/token",
                    data={
                        "grant_type": "client_credentials",
                        "client_id": self.client_id,
                        "client_secret": self.client_secret
                    }
                )
                
                if response.status_code != 200:
                    logger.error(f"Authentication failed: {response.text}")
                    raise Exception(f"Authentication failed: {response.status_code}")
                
                token_data = response.json()
                self.token = token_data["access_token"]
                expires_in = token_data.get("expires_in", 3600)
                self.token_expiry = datetime.now() + timedelta(seconds=expires_in)
                
                logger.info("Successfully authenticated with FEMA Grants Portal API")
                return self.token
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise
    
    async def get_projects(self, disaster_id=None, status=None, page=1, limit=100):
        """Get projects from FEMA Grants Portal"""
        await self.authenticate()
        
        params = {"page": page, "limit": limit}
        if disaster_id:
            params["disaster_id"] = disaster_id
        if status:
            params["status"] = status
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/projects",
                    params=params,
                    headers={"Authorization": f"Bearer {self.token}"}
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get projects: {response.text}")
                    raise Exception(f"Failed to get projects: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error getting projects: {str(e)}")
            raise
    
    async def get_project_details(self, project_id):
        """Get details of a specific project"""
        await self.authenticate()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/projects/{project_id}",
                    headers={"Authorization": f"Bearer {self.token}"}
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get project details: {response.text}")
                    raise Exception(f"Failed to get project details: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error getting project details: {str(e)}")
            raise
    
    async def submit_document(self, project_id, document_type, file_path, metadata=None):
        """Submit a document to a project"""
        await self.authenticate()
        
        try:
            # Read file content
            with open(file_path, "rb") as f:
                file_content = f.read()
            
            # Prepare form data
            form_data = {
                "document_type": document_type,
                "file": (file_path.split("/")[-1], file_content)
            }
            
            if metadata:
                form_data["metadata"] = json.dumps(metadata)
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/projects/{project_id}/documents",
                    files=form_data,
                    headers={"Authorization": f"Bearer {self.token}"}
                )
                
                if response.status_code not in (200, 201):
                    logger.error(f"Failed to submit document: {response.text}")
                    raise Exception(f"Failed to submit document: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error submitting document: {str(e)}")
            raise
    
    async def get_project_documents(self, project_id, document_type=None, page=1, limit=100):
        """Get documents for a project"""
        await self.authenticate()
        
        params = {"page": page, "limit": limit}
        if document_type:
            params["document_type"] = document_type
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/projects/{project_id}/documents",
                    params=params,
                    headers={"Authorization": f"Bearer {self.token}"}
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get project documents: {response.text}")
                    raise Exception(f"Failed to get project documents: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error getting project documents: {str(e)}")
            raise
    
    async def download_document(self, document_id, output_path=None):
        """Download a document"""
        await self.authenticate()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/documents/{document_id}/download",
                    headers={"Authorization": f"Bearer {self.token}"}
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to download document: {response.text}")
                    raise Exception(f"Failed to download document: {response.status_code}")
                
                if output_path:
                    with open(output_path, "wb") as f:
                        f.write(response.content)
                    return {"success": True, "path": output_path}
                else:
                    return response.content
        except Exception as e:
            logger.error(f"Error downloading document: {str(e)}")
            raise
```

```python
# document_management_connector.py
import httpx
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import asyncio
import base64
import mimetypes
import os

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentManagementConnector:
    """Connector for Document Management System API"""
    
    def __init__(self, api_key, base_url, system_type="sharepoint"):
        self.api_key = api_key
        self.base_url = base_url
        self.system_type = system_type
    
    async def get_folders(self, parent_path=None):
        """Get folders from the document management system"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}",
            "Content-Type": "application/json"
        }
        
        params = {}
        if parent_path:
            params["parent_path"] = parent_path
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/folders",
                    params=params,
                    headers=headers
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get folders: {response.text}")
                    raise Exception(f"Failed to get folders: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error getting folders: {str(e)}")
            raise
    
    async def create_folder(self, folder_path, metadata=None):
        """Create a new folder"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "path": folder_path
        }
        
        if metadata:
            data["metadata"] = metadata
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/folders",
                    json=data,
                    headers=headers
                )
                
                if response.status_code not in (200, 201):
                    logger.error(f"Failed to create folder: {response.text}")
                    raise Exception(f"Failed to create folder: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error creating folder: {str(e)}")
            raise
    
    async def get_documents(self, folder_path=None, query=None, page=1, limit=100):
        """Get documents from the document management system"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}",
            "Content-Type": "application/json"
        }
        
        params = {
            "page": page,
            "limit": limit
        }
        
        if folder_path:
            params["folder_path"] = folder_path
        
        if query:
            params["query"] = query
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/documents",
                    params=params,
                    headers=headers
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to get documents: {response.text}")
                    raise Exception(f"Failed to get documents: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error getting documents: {str(e)}")
            raise
    
    async def upload_document(self, file_path, folder_path, metadata=None):
        """Upload a document to the document management system"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}"
        }
        
        try:
            # Get file name and mime type
            file_name = os.path.basename(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # Read file content
            with open(file_path, "rb") as f:
                file_content = f.read()
            
            # Prepare form data
            form_data = {
                "folder_path": folder_path
            }
            
            if metadata:
                form_data["metadata"] = json.dumps(metadata)
            
            files = {
                "file": (file_name, file_content, mime_type)
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/documents",
                    data=form_data,
                    files=files,
                    headers=headers
                )
                
                if response.status_code not in (200, 201):
                    logger.error(f"Failed to upload document: {response.text}")
                    raise Exception(f"Failed to upload document: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error uploading document: {str(e)}")
            raise
    
    async def download_document(self, document_id, output_path=None):
        """Download a document from the document management system"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/documents/{document_id}/content",
                    headers=headers
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to download document: {response.text}")
                    raise Exception(f"Failed to download document: {response.status_code}")
                
                if output_path:
                    with open(output_path, "wb") as f:
                        f.write(response.content)
                    return {"success": True, "path": output_path}
                else:
                    return response.content
        except Exception as e:
            logger.error(f"Error downloading document: {str(e)}")
            raise
    
    async def update_document_metadata(self, document_id, metadata):
        """Update document metadata"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.patch(
                    f"{self.base_url}/documents/{document_id}/metadata",
                    json=metadata,
                    headers=headers
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to update document metadata: {response.text}")
                    raise Exception(f"Failed to update document metadata: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error updating document metadata: {str(e)}")
            raise
    
    async def search_documents(self, query, folder_path=None, document_type=None, page=1, limit=100):
        """Search for documents"""
        headers = {
            "Authorization": f"ApiKey {self.api_key}",
            "Content-Type": "application/json"
        }
        
        params = {
            "query": query,
            "page": page,
            "limit": limit
        }
        
        if folder_path:
            params["folder_path"] = folder_path
        
        if document_type:
            params["document_type"] = document_type
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/documents/search",
                    params=params,
                    headers=headers
                )
                
                if response.status_code != 200:
                    logger.error(f"Failed to search documents: {response.text}")
                    raise Exception(f"Failed to search documents: {response.status_code}")
                
                return response.json()
        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            raise
```

### 3.4 Integration Management UI Implementation

```javascript
// IntegrationDashboard.jsx (React component)
import React, { useState, useEffect } from 'react';
import { 
  Layout, 
  Menu, 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Tabs, 
  Badge, 
  Tooltip, 
  message 
} from 'antd';
import { 
  ApiOutlined, 
  LinkOutlined, 
  SettingOutlined, 
  HistoryOutlined, 
  PlusOutlined, 
  SyncOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined 
} from '@ant-design/icons';
import axios from 'axios';

const { Header, Sider, Content } = Layout;
const { TabPane } = Tabs;
const { Option } = Select;

const IntegrationDashboard = () => {
  const [activeTab, setActiveTab] = useState('systems');
  const [integrationSystems, setIntegrationSystems] = useState([]);
  const [webhooks, setWebhooks] = useState([]);
  const [webhookEvents, setWebhookEvents] = useState([]);
  const [webhookDeliveries, setWebhookDeliveries] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalType, setModalType] = useState('');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    fetchData();
  }, [activeTab]);
  
  const fetchData = async () => {
    setLoading(true);
    try {
      if (activeTab === 'systems' || activeTab === '') {
        const response = await axios.get('/api/integrations');
        setIntegrationSystems(response.data);
      } else if (activeTab === 'webhooks') {
        const response = await axios.get('/api/webhooks/subscriptions');
        setWebhooks(response.data);
      } else if (activeTab === 'events') {
        const response = await axios.get('/api/webhooks/events');
        setWebhookEvents(response.data);
      } else if (activeTab === 'deliveries') {
        const response = await axios.get('/api/webhooks/deliveries');
        setWebhookDeliveries(response.data);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      message.error('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };
  
  const showModal = (type) => {
    setModalType(type);
    setIsModalVisible(true);
    form.resetFields();
  };
  
  const handleCancel = () => {
    setIsModalVisible(false);
  };
  
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (modalType === 'webhook') {
        await axios.post('/api/webhooks/subscriptions', {
          name: values.name,
          event_type: values.event_type,
          target_url: values.target_url,
          secret: values.secret,
          headers: values.headers ? JSON.parse(values.headers) : undefined
        });
        message.success('Webhook subscription created successfully');
        fetchData();
      }
      
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error submitting form:', error);
      message.error('Failed to submit form');
    }
  };
  
  const integrationSystemsColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'System ID',
      dataIndex: 'system_id',
      key: 'system_id',
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Badge 
          status={active ? 'success' : 'error'} 
          text={active ? 'Active' : 'Inactive'} 
        />
      ),
    },
    {
      title: 'Endpoints',
      dataIndex: 'endpoints',
      key: 'endpoints',
      render: (endpoints) => endpoints.length,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div>
          <Button 
            type="link" 
            onClick={() => viewSystemDetails(record.system_id)}
          >
            View Details
          </Button>
          <Button 
            type="link" 
            onClick={() => testConnection(record.system_id)}
          >
            Test Connection
          </Button>
        </div>
      ),
    },
  ];
  
  const webhooksColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Event Type',
      dataIndex: 'event_type',
      key: 'event_type',
    },
    {
      title: 'Target URL',
      dataIndex: 'target_url',
      key: 'target_url',
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'active',
      key: 'active',
      render: (active) => (
        <Badge 
          status={active ? 'success' : 'error'} 
          text={active ? 'Active' : 'Inactive'} 
        />
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div>
          <Button 
            type="link" 
            onClick={() => toggleWebhookStatus(record.id, !record.active)}
          >
            {record.active ? 'Deactivate' : 'Activate'}
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => deleteWebhook(record.id)}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];
  
  const eventsColumns = [
    {
      title: 'Event ID',
      dataIndex: 'id',
      key: 'id',
      ellipsis: true,
    },
    {
      title: 'Event Type',
      dataIndex: 'event_type',
      key: 'event_type',
    },
    {
      title: 'Created At',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button 
          type="link" 
          onClick={() => viewEventDetails(record.id)}
        >
          View Details
        </Button>
      ),
    },
  ];
  
  const deliveriesColumns = [
    {
      title: 'Webhook',
      dataIndex: 'webhook_id',
      key: 'webhook_id',
      render: (webhook_id) => {
        const webhook = webhooks.find(w => w.id === webhook_id);
        return webhook ? webhook.name : webhook_id;
      },
    },
    {
      title: 'Event',
      dataIndex: 'event_id',
      key: 'event_id',
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        let icon, color;
        if (status === 'success') {
          icon = <CheckCircleOutlined />;
          color = 'green';
        } else if (status === 'failed') {
          icon = <CloseCircleOutlined />;
          color = 'red';
        } else {
          icon = <SyncOutlined spin />;
          color = 'blue';
        }
        return (
          <span style={{ color }}>
            {icon} {status}
          </span>
        );
      },
    },
    {
      title: 'Response Code',
      dataIndex: 'response_code',
      key: 'response_code',
    },
    {
      title: 'Delivery Time (s)',
      dataIndex: 'delivery_time',
      key: 'delivery_time',
      render: (time) => time ? time.toFixed(3) : '-',
    },
    {
      title: 'Created At',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Button 
          type="link" 
          onClick={() => viewDeliveryDetails(record.id)}
        >
          View Details
        </Button>
      ),
    },
  ];
  
  const viewSystemDetails = (systemId) => {
    // Implementation for viewing system details
  };
  
  const testConnection = async (systemId) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/integrations/test-connection', {
        system_id: systemId
      });
      
      if (response.data.success) {
        message.success('Connection test successful');
      } else {
        message.error(`Connection test failed: ${response.data.error}`);
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      message.error('Connection test failed');
    } finally {
      setLoading(false);
    }
  };
  
  const toggleWebhookStatus = async (webhookId, active) => {
    try {
      setLoading(true);
      await axios.patch(`/api/webhooks/subscriptions/${webhookId}`, {
        active
      });
      
      message.success(`Webhook ${active ? 'activated' : 'deactivated'} successfully`);
      fetchData();
    } catch (error) {
      console.error('Error toggling webhook status:', error);
      message.error('Failed to update webhook status');
    } finally {
      setLoading(false);
    }
  };
  
  const deleteWebhook = async (webhookId) => {
    try {
      setLoading(true);
      await axios.delete(`/api/webhooks/subscriptions/${webhookId}`);
      
      message.success('Webhook deleted successfully');
      fetchData();
    } catch (error) {
      console.error('Error deleting webhook:', error);
      message.error('Failed to delete webhook');
    } finally {
      setLoading(false);
    }
  };
  
  const viewEventDetails = (eventId) => {
    // Implementation for viewing event details
  };
  
  const viewDeliveryDetails = (deliveryId) => {
    // Implementation for viewing delivery details
  };
  
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider width={200} theme="light">
        <Menu
          mode="inline"
          selectedKeys={[activeTab]}
          style={{ height: '100%', borderRight: 0 }}
          onSelect={({ key }) => setActiveTab(key)}
        >
          <Menu.Item key="systems" icon={<ApiOutlined />}>
            Integration Systems
          </Menu.Item>
          <Menu.Item key="webhooks" icon={<LinkOutlined />}>
            Webhooks
          </Menu.Item>
          <Menu.Item key="events" icon={<HistoryOutlined />}>
            Events
          </Menu.Item>
          <Menu.Item key="deliveries" icon={<SyncOutlined />}>
            Deliveries
          </Menu.Item>
        </Menu>
      </Sider>
      <Layout style={{ padding: '0 24px 24px' }}>
        <Header style={{ background: '#fff', padding: '0 20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h2>Integration Management</h2>
          {activeTab === 'webhooks' && (
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => showModal('webhook')}
            >
              Add Webhook
            </Button>
          )}
        </Header>
        <Content
          style={{
            background: '#fff',
            padding: 24,
            margin: '16px 0',
            minHeight: 280,
          }}
        >
          {activeTab === 'systems' && (
            <Table 
              columns={integrationSystemsColumns} 
              dataSource={integrationSystems}
              rowKey="system_id"
              loading={loading}
            />
          )}
          
          {activeTab === 'webhooks' && (
            <Table 
              columns={webhooksColumns} 
              dataSource={webhooks}
              rowKey="id"
              loading={loading}
            />
          )}
          
          {activeTab === 'events' && (
            <Table 
              columns={eventsColumns} 
              dataSource={webhookEvents}
              rowKey="id"
              loading={loading}
            />
          )}
          
          {activeTab === 'deliveries' && (
            <Table 
              columns={deliveriesColumns} 
              dataSource={webhookDeliveries}
              rowKey="id"
              loading={loading}
            />
          )}
        </Content>
      </Layout>
      
      <Modal
        title={modalType === 'webhook' ? 'Add Webhook Subscription' : ''}
        visible={isModalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        {modalType === 'webhook' && (
          <Form form={form} layout="vertical">
            <Form.Item
              name="name"
              label="Name"
              rules={[{ required: true, message: 'Please enter a name' }]}
            >
              <Input placeholder="Webhook name" />
            </Form.Item>
            
            <Form.Item
              name="event_type"
              label="Event Type"
              rules={[{ required: true, message: 'Please select an event type' }]}
            >
              <Select placeholder="Select event type">
                <Option value="project.created">Project Created</Option>
                <Option value="project.updated">Project Updated</Option>
                <Option value="document.uploaded">Document Uploaded</Option>
                <Option value="document.approved">Document Approved</Option>
                <Option value="document.rejected">Document Rejected</Option>
                <Option value="compliance.status_changed">Compliance Status Changed</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="target_url"
              label="Target URL"
              rules={[
                { required: true, message: 'Please enter a target URL' },
                { type: 'url', message: 'Please enter a valid URL' }
              ]}
            >
              <Input placeholder="https://example.com/webhook" />
            </Form.Item>
            
            <Form.Item
              name="secret"
              label="Secret (Optional)"
              tooltip="Used to sign webhook payloads for verification"
            >
              <Input.Password placeholder="Webhook secret" />
            </Form.Item>
            
            <Form.Item
              name="headers"
              label="Custom Headers (Optional)"
              tooltip="JSON object of custom headers to include in webhook requests"
            >
              <Input.TextArea 
                placeholder='{"X-Custom-Header": "value"}'
                rows={3}
              />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </Layout>
  );
};

export default IntegrationDashboard;
```

### 3.5 Implementation Timeline

| Phase | Duration | Activities |
|-------|----------|------------|
| Planning & Design | 3 weeks | Design API gateway architecture, define integration points, design data transformation schemas |
| API Gateway Development | 4 weeks | Implement API gateway, authentication, rate limiting, logging |
| Data Transformation Service | 3 weeks | Implement data transformation service, mapping engine, custom functions |
| Webhook Service | 2 weeks | Implement webhook subscription, event delivery, retry logic |
| Integration Connectors | 4 weeks | Implement connectors for FEMA Grants Portal, document management systems, etc. |
| Integration Management UI | 3 weeks | Develop integration dashboard, webhook management, monitoring tools |
| Testing & QA | 2 weeks | Comprehensive testing of integration features |
| Deployment | 1 week | Deploy to production environment |
| Documentation & Training | 2 weeks | Create API documentation, integration guides, conduct training sessions |

## 4. Mobile Features for Field Operations

### 4.1 Overview

The mobile features module will extend ComplianceMax capabilities to field operations, enabling users to capture data, access compliance information, and manage tasks while on-site at disaster areas or project locations.

### 4.2 Technical Architecture

#### Mobile App Core Implementation (React Native)

```javascript
// App.js (React Native)
import React, { useState, useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// Auth Screens
import LoginScreen from './screens/auth/LoginScreen';
import RegisterScreen from './screens/auth/RegisterScreen';
import ForgotPasswordScreen from './screens/auth/ForgotPasswordScreen';

// Main Screens
import DashboardScreen from './screens/main/DashboardScreen';
import ProjectsScreen from './screens/main/ProjectsScreen';
import ProjectDetailScreen from './screens/main/ProjectDetailScreen';
import DocumentsScreen from './screens/main/DocumentsScreen';
import DocumentDetailScreen from './screens/main/DocumentDetailScreen';
import TasksScreen from './screens/main/TasksScreen';
import TaskDetailScreen from './screens/main/TaskDetailScreen';
import CaptureScreen from './screens/main/CaptureScreen';
import SettingsScreen from './screens/main/SettingsScreen';

// Context
import { AuthProvider, useAuth } from './context/AuthContext';
import { OfflineProvider } from './context/OfflineContext';

// Theme
import { theme } from './theme';

// Navigation
const AuthStack = createStackNavigator();
const MainStack = createStackNavigator();
const Tab = createBottomTabNavigator();

const AuthNavigator = () => (
  <AuthStack.Navigator
    screenOptions={{
      headerShown: false,
    }}
  >
    <AuthStack.Screen name="Login" component={LoginScreen} />
    <AuthStack.Screen name="Register" component={RegisterScreen} />
    <AuthStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
  </AuthStack.Navigator>
);

const ProjectsNavigator = () => (
  <MainStack.Navigator>
    <MainStack.Screen name="Projects" component={ProjectsScreen} />
    <MainStack.Screen name="ProjectDetail" component={ProjectDetailScreen} />
    <MainStack.Screen name="Documents" component={DocumentsScreen} />
    <MainStack.Screen name="DocumentDetail" component={DocumentDetailScreen} />
  </MainStack.Navigator>
);

const TasksNavigator = () => (
  <MainStack.Navigator>
    <MainStack.Screen name="Tasks" component={TasksScreen} />
    <MainStack.Screen name="TaskDetail" component={TaskDetailScreen} />
  </MainStack.Navigator>
);

const MainNavigator = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;

        if (route.name === 'Dashboard') {
          iconName = 'view-dashboard';
        } else if (route.name === 'ProjectsTab') {
          iconName = 'folder';
        } else if (route.name === 'TasksTab') {
          iconName = 'clipboard-check';
        } else if (route.name === 'Capture') {
          iconName = 'camera';
        } else if (route.name === 'Settings') {
          iconName = 'cog';
        }

        return <Icon name={iconName} size={size} color={color} />;
      },
    })}
    tabBarOptions={{
      activeTintColor: theme.colors.primary,
      inactiveTintColor: 'gray',
    }}
  >
    <Tab.Screen name="Dashboard" component={DashboardScreen} />
    <Tab.Screen name="ProjectsTab" component={ProjectsNavigator} options={{ title: 'Projects' }} />
    <Tab.Screen name="TasksTab" component={TasksNavigator} options={{ title: 'Tasks' }} />
    <Tab.Screen name="Capture" component={CaptureScreen} />
    <Tab.Screen name="Settings" component={SettingsScreen} />
  </Tab.Navigator>
);

const AppNavigator = () => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return null; // Or a loading screen
  }

  return (
    <NavigationContainer>
      {isAuthenticated ? <MainNavigator /> : <AuthNavigator />}
    </NavigationContainer>
  );
};

const App = () => {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <AuthProvider>
          <OfflineProvider>
            <AppNavigator />
          </OfflineProvider>
        </AuthProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
};

export default App;
```

#### Authentication and Offline Context Implementation

```javascript
// context/AuthContext.js
import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { API_URL } from '../config';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const checkLoginStatus = async () => {
      try {
        const storedToken = await AsyncStorage.getItem('token');
        const storedUser = await AsyncStorage.getItem('user');
        
        if (storedToken && storedUser) {
          setToken(storedToken);
          setUser(JSON.parse(storedUser));
          setIsAuthenticated(true);
          
          // Set axios default header
          axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;
        }
      } catch (error) {
        console.error('Error checking login status:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkLoginStatus();
  }, []);

  const login = async (email, password) => {
    try {
      const response = await axios.post(`${API_URL}/auth/login`, {
        email,
        password,
      });
      
      const { token, user } = response.data;
      
      // Store token and user info
      await AsyncStorage.setItem('token', token);
      await AsyncStorage.setItem('user', JSON.stringify(user));
      
      // Set axios default header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      
      setToken(token);
      setUser(user);
      setIsAuthenticated(true);
      
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Login failed',
      };
    }
  };

  const logout = async () => {
    try {
      // Clear storage
      await AsyncStorage.removeItem('token');
      await AsyncStorage.removeItem('user');
      
      // Clear axios default header
      delete axios.defaults.headers.common['Authorization'];
      
      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const register = async (userData) => {
    try {
      const response = await axios.post(`${API_URL}/auth/register`, userData);
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Registration error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Registration failed',
      };
    }
  };

  const forgotPassword = async (email) => {
    try {
      const response = await axios.post(`${API_URL}/auth/forgot-password`, { email });
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Forgot password error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Password reset request failed',
      };
    }
  };

  const updateProfile = async (userData) => {
    try {
      const response = await axios.put(`${API_URL}/users/profile`, userData);
      
      // Update stored user info
      const updatedUser = { ...user, ...response.data };
      await AsyncStorage.setItem('user', JSON.stringify(updatedUser));
      
      setUser(updatedUser);
      
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Profile update failed',
      };
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        token,
        isLoading,
        login,
        logout,
        register,
        forgotPassword,
        updateProfile,
      }}
    >
      {children}
    