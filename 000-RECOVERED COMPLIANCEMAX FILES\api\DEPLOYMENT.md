# ComplianceMax V74 - Enterprise Deployment Guide

## 🚀 Quick Start

```bash
# 1. Clone and setup
git clone <repository>
cd June_2025-ComplianceMax

# 2. Run automated setup
./setup.sh

# 3. Access application
open http://localhost:8000
```

## 📋 Infrastructure Overview

### **Database Migration: MongoDB → PostgreSQL**
- **Enhanced Performance**: Complex relational queries optimized
- **ACID Compliance**: Critical for FEMA compliance data integrity  
- **Advanced Indexing**: Faster conditional logic processing
- **JSON Support**: Maintains document flexibility where needed

### **AI-Powered Document Processing**
- **xAI Integration**: Your existing API key configured for Grok-beta
- **Docling Framework**: IBM's enterprise OCR replacing Tesseract.js
- **Multi-format Support**: PDF, Office docs, images with auto-categorization
- **Intelligent Tagging**: Automated FEMA category detection

### **Enterprise Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js UI   │────│  FastAPI App    │────│   PostgreSQL    │
│   Port 3000     │    │   Port 8000     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Celery Worker  │────│     Redis       │
                       │  Doc Processing │    │   Port 6379     │
                       └─────────────────┘    └─────────────────┘
```

## ⚙️ Configuration Files

### **config.env** - Master Configuration
```bash
# Key features configured:
- PostgreSQL with connection pooling
- xAI API integration (Grok-beta)
- Docling document processing
- FEMA compliance engine
- Email notifications (<EMAIL>)
- Redis multi-database setup
- Feature flags for controlled rollouts
```

### **docker-compose.yml** - Service Orchestration
```yaml
services:
  - postgres: Database with persistent volumes
  - redis: Cache & session store
  - app: Main FastAPI application
  - worker: Document processing pipeline
  - frontend: Next.js UI (from ALL NEW APP)
```

### **requirements.txt** - Dependencies
```
Key packages:
- fastapi: Core API framework
- docling: IBM document processing
- sqlalchemy: PostgreSQL ORM
- celery: Background tasks
- openai: xAI integration
- redis: Caching layer
```

## 🔧 Setup Options

### **Option 1: Automated Setup (Recommended)**
```bash
./setup.sh
```

### **Option 2: Manual Setup**
```bash
# 1. Environment
cp config.env .env

# 2. Directories
mkdir -p uploads storage cache temp logs database/init

# 3. Services
docker-compose up -d

# 4. Database
docker-compose exec app alembic upgrade head
```

### **Option 3: Development Mode**
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Setup database
createdb compliancemax
psql compliancemax < database/schema.sql

# 3. Run application
uvicorn main:app --reload
```

## 📊 FEMA Compliance Features

### **PAPPG Version Management**
- Automatic version determination based on incident dates
- Support for v1.0 through v5.0 regulations
- Backward compatibility for legacy cases

### **Conditional Logic Engine**
```
IF trigger_condition_met THEN action_required
├── 20+ checkbox state tracking
├── Workflow automation
├── Compliance validation
└── Deadline management
```

### **Document Processing Pipeline**
```
Upload → Docling OCR → xAI Analysis → Auto-Categorization → Compliance Check
```

## 🛠️ Management Commands

### **Application Management**
```bash
# View logs
docker-compose logs -f app

# Database operations
docker-compose exec app alembic upgrade head
docker-compose exec postgres psql -U postgres compliancemax

# Worker monitoring
docker-compose logs -f worker

# Cache management
docker-compose exec redis redis-cli
```

### **Development Tools**
```bash
# Code formatting
black .
flake8 .

# Testing
pytest tests/

# Database migration
alembic revision --autogenerate -m "description"
alembic upgrade head
```

## 🔒 Security Configuration

### **Environment Variables to Update**
```bash
# Generate new secret key
SECRET_KEY=your-production-secret-key

# Database credentials
POSTGRESQL_URL=********************************/db

# Email configuration
SMTP_PASSWORD=your-app-specific-password

# xAI API (already configured)
XAI_API_KEY=************************************************************************************
```

### **Production Checklist**
- [ ] Update SECRET_KEY
- [ ] Configure SSL certificates
- [ ] Set DEBUG=False
- [ ] Update database credentials
- [ ] Configure backup strategy
- [ ] Set up monitoring
- [ ] Configure log rotation
- [ ] Update CORS origins

## 📁 Directory Structure

```
ComplianceMax/
├── config.env              # Master configuration
├── docker-compose.yml      # Service orchestration
├── requirements.txt        # Python dependencies
├── Dockerfile              # Application container
├── setup.sh               # Automated setup script
├── 
├── ALL NEW APP/           # Next.js frontend
├── DOCS/                  # Compliance specifications
├── database/              # Schema and migrations
├── uploads/               # File uploads
├── storage/               # Persistent storage
├── cache/                 # Docling cache
├── temp/                  # Temporary files
└── logs/                  # Application logs
```

## 🚨 Troubleshooting

### **Common Issues**

**Database Connection**
```bash
# Check PostgreSQL status
docker-compose exec postgres pg_isready

# View database logs
docker-compose logs postgres
```

**Document Processing**
```bash
# Check Docling installation
docker-compose exec app python -c "import docling; print('OK')"

# Monitor worker queue
docker-compose exec redis redis-cli monitor
```

**xAI Integration**
```bash
# Test API connection
curl -H "Authorization: Bearer $XAI_API_KEY" https://api.x.ai/v1/models
```

## 📈 Monitoring & Performance

### **Health Checks**
- Application: `http://localhost:8000/health`
- Database: `docker-compose exec postgres pg_isready`
- Redis: `docker-compose exec redis redis-cli ping`

### **Performance Monitoring**
- Prometheus metrics: `/metrics`
- Slow query logging: Enabled
- Memory monitoring: Enabled
- Rate limiting: 1000/hour per user

## 🔄 Migration from Original System

### **From MongoDB to PostgreSQL**
1. Export existing MongoDB data
2. Transform to relational schema
3. Import using migration scripts
4. Verify conditional logic preservation

### **From Tesseract.js to Docling**
1. Update document processing pipeline
2. Migrate existing processed documents
3. Reprocess critical documents with Docling
4. Verify OCR accuracy improvements

---

## 📞 Support

For technical support or questions about the ComplianceMax deployment:
- Review logs: `docker-compose logs -f`
- Check configuration: `cat .env`
- Database status: `docker-compose exec postgres psql -U postgres -c "\l"`
- Redis status: `docker-compose exec redis redis-cli info`

**Magic Applied** ✨ - Your ComplianceMax V74 enterprise infrastructure is ready for deployment! 