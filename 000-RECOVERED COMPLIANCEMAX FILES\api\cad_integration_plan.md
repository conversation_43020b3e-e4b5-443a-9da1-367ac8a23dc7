# CAD Integration Plan for PA-CHECK-MM

## 1. Current Document Processing Capabilities

Based on the analysis of the PA-CHECK-MM codebase, the system currently has the following architecture and capabilities:

### System Architecture
- **Workflow Engine**: Core component that manages workflow definitions and instances
- **Rule Engine**: Evaluates conditions for workflow transitions
- **API Layer**: FastAPI-based REST API for workflow management
- **Frontend**: Next.js-based UI for workflow configuration and monitoring

### Current Document Processing Capabilities
The current system does not appear to have any dedicated document processing capabilities. The codebase analysis reveals:

- No document upload endpoints in the API
- No document parsing or processing libraries in requirements.txt
- No file handling code beyond loading workflow definitions from JSON files
- No document storage or retrieval mechanisms

The system is primarily focused on workflow automation with a rule engine for defining and managing workflow rules, but lacks specific document handling functionality.

## 2. Required Libraries and Tools for CAD File Processing

To add support for reading and analyzing architectural/engineering CAD files, plans, and specifications, the following libraries and tools will be needed:

### DXF File Processing
- **ezdxf**: A Python library for reading and writing DXF files
  - Supports multiple DXF versions (R12, R2000, R2004, R2007, R2010, R2013, R2018)
  - Provides access to entities such as lines, polylines, arcs, circles, text, and blocks
  - Handles attributes, XData, and object data

### DWG File Processing
- **ODA File Converter**: A command-line tool to convert DWG files to DXF format
  - Required as an external dependency since DWG is a proprietary format
  - Can be integrated with ezdxf for processing DWG files by first converting them to DXF

### IFC/BIM File Processing
- **ifcopenshell**: A Python library for working with IFC (Industry Foundation Classes) files
  - Provides parsing, manipulation, and analysis of IFC models
  - Supports IFC2x3 TC1, IFC4 Add2 TC1, IFC4x1, IFC4x2, and IFC4x3 Add2 schemas
  - Includes geometric modeling and analysis capabilities

### PDF and Document Processing
- **PyMuPDF (fitz)**: For handling PDF documents that may be embedded in or accompany CAD files
- **python-docx**: For processing Microsoft Word documents containing specifications
- **pandas**: For data extraction and manipulation from tabular data

### File Type Detection
- **python-magic**: For MIME type detection to identify file types
- **filetype**: A package to infer file type and extension based on binary signatures

### Image Processing
- **Pillow**: For handling raster images that may be part of CAD files
- **OpenCV**: For more advanced image processing and analysis

## 3. Necessary Code Changes and Additions

### New Modules to Create

1. **Document Processing Module**
   - `src/document_processor/`: Main package for document processing
   - `src/document_processor/__init__.py`: Package initialization
   - `src/document_processor/file_handler.py`: File type detection and routing
   - `src/document_processor/cad_processor.py`: CAD file processing (DXF, DWG)
   - `src/document_processor/ifc_processor.py`: IFC/BIM file processing
   - `src/document_processor/pdf_processor.py`: PDF document processing
   - `src/document_processor/docx_processor.py`: Word document processing
   - `src/document_processor/metadata_extractor.py`: Extract metadata from various file types
   - `src/document_processor/utils.py`: Utility functions for document processing

2. **Storage Module**
   - `src/storage/`: Package for file storage
   - `src/storage/__init__.py`: Package initialization
   - `src/storage/file_storage.py`: File storage interface
   - `src/storage/local_storage.py`: Local file system storage implementation
   - `src/storage/s3_storage.py`: Amazon S3 storage implementation (optional)

3. **API Extensions**
   - `src/api/routers/document.py`: API endpoints for document upload, processing, and retrieval
   - `src/api/models/document.py`: Pydantic models for document-related requests and responses

### Modifications to Existing Code

1. **API Layer**
   - Update `src/api/main.py` to include the new document router
   - Add document-related error handling

2. **Workflow Engine**
   - Extend `src/workflow/workflow_engine.py` to support document-related workflow states and transitions
   - Add document processing capabilities to workflow context

3. **Requirements**
   - Update `requirements.txt` to include the new dependencies

4. **Configuration**
   - Add configuration for document storage paths, supported file types, etc.

## 4. Implementation Plan

### Phase 1: Setup and Basic File Handling

1. **Update Dependencies**
   ```python
   # Add to requirements.txt
   ezdxf>=1.0.0
   ifcopenshell>=0.7.0
   PyMuPDF>=1.20.0
   python-docx>=0.8.11
   pandas>=1.5.0
   python-magic>=0.4.27
   filetype>=1.2.0
   Pillow>=9.0.0
   opencv-python>=4.7.0
   python-multipart>=0.0.5  # For FastAPI file uploads
   ```

2. **Create File Storage Module**
   - Implement `src/storage/file_storage.py` with abstract interface
   - Implement `src/storage/local_storage.py` for local file system storage

3. **Create Basic File Handler**
   - Implement `src/document_processor/file_handler.py` for file type detection and routing
   - Add support for basic file operations (upload, download, delete)

### Phase 2: CAD File Processing

1. **Implement DXF Processing**
   - Create `src/document_processor/cad_processor.py` with DXF parsing functionality
   - Add metadata extraction for DXF files
   - Implement entity extraction (lines, polylines, text, etc.)

2. **Add DWG Support via ODA File Converter**
   - Set up ODA File Converter integration
   - Implement DWG to DXF conversion workflow
   - Process converted DXF files using the DXF processor

3. **Implement IFC/BIM Processing**
   - Create `src/document_processor/ifc_processor.py` with IFC parsing functionality
   - Add metadata extraction for IFC files
   - Implement entity extraction and geometric analysis

### Phase 3: Document Processing and API Integration

1. **Implement PDF and Document Processing**
   - Create `src/document_processor/pdf_processor.py` for PDF processing
   - Create `src/document_processor/docx_processor.py` for Word document processing
   - Implement text extraction and metadata retrieval

2. **Create API Endpoints**
   - Implement `src/api/routers/document.py` with endpoints for:
     - File upload
     - File download
     - File processing
     - Metadata retrieval
     - Entity extraction

3. **Update API Models**
   - Create `src/api/models/document.py` with models for:
     - Document upload requests
     - Document metadata responses
     - Processing results

### Phase 4: Workflow Integration

1. **Extend Workflow Engine**
   - Update `src/workflow/workflow_engine.py` to handle document-related states
   - Add document processing actions to workflow transitions

2. **Create Document-Specific Workflow Templates**
   - Define workflow templates for CAD file processing
   - Create templates for document approval processes

### Phase 5: Frontend Integration

1. **Add Document Upload Components**
   - Create file upload UI components
   - Implement drag-and-drop functionality

2. **Add Document Viewer Components**
   - Implement basic CAD viewer (possibly using a JavaScript library)
   - Add PDF and document viewers

3. **Create Document Management UI**
   - Implement document listing and filtering
   - Add metadata display and editing

## 5. Testing Strategy

### Unit Testing

1. **File Handler Tests**
   - Test file type detection
   - Test file routing to appropriate processors

2. **CAD Processor Tests**
   - Test DXF parsing with sample files
   - Test DWG conversion and processing
   - Test IFC parsing and analysis

3. **Document Processor Tests**
   - Test PDF text extraction
   - Test Word document processing
   - Test metadata extraction

4. **API Endpoint Tests**
   - Test file upload endpoints
   - Test processing endpoints
   - Test metadata retrieval

### Integration Testing

1. **End-to-End Document Processing**
   - Test complete document processing workflow
   - Verify correct handling of different file types

2. **Workflow Integration**
   - Test document-related workflow states and transitions
   - Verify document context in workflow instances

3. **Storage Integration**
   - Test file storage and retrieval
   - Verify proper cleanup of temporary files

### Performance Testing

1. **Large File Handling**
   - Test processing of large CAD files
   - Measure memory usage and processing time

2. **Concurrent Processing**
   - Test multiple simultaneous document uploads
   - Verify system stability under load

### Sample Test Files

Create a collection of test files for various scenarios:

1. **DXF Test Files**
   - Simple architectural plans
   - Complex multi-layer drawings
   - Files with different entity types

2. **DWG Test Files**
   - Files from different AutoCAD versions
   - Files with blocks and attributes

3. **IFC Test Files**
   - Building models with different complexity
   - Files with various metadata

4. **Mixed Document Sets**
   - Combinations of CAD files, PDFs, and specifications
   - Project documentation sets

## 6. Specific File Modifications

### New Files to Create

```
src/
├── document_processor/
│   ├── __init__.py
│   ├── file_handler.py
│   ├── cad_processor.py
│   ├── ifc_processor.py
│   ├── pdf_processor.py
│   ├── docx_processor.py
│   ├── metadata_extractor.py
│   └── utils.py
├── storage/
│   ├── __init__.py
│   ├── file_storage.py
│   ├── local_storage.py
│   └── s3_storage.py
└── api/
    ├── models/
    │   └── document.py
    └── routers/
        └── document.py
```

### Files to Modify

1. **src/api/main.py**
   - Add import for document router
   - Include document router in FastAPI app

2. **src/api/models.py**
   - Add imports for document models
   - Update error handling for document-related errors

3. **requirements.txt**
   - Add new dependencies for document processing

4. **src/workflow/workflow_engine.py**
   - Add document processing capabilities
   - Update workflow context handling

## 7. Conclusion

Integrating CAD file processing capabilities into the PA-CHECK-MM system will require significant additions to the codebase, including new modules for document processing, file storage, and API endpoints. The implementation plan outlines a phased approach, starting with basic file handling and progressing to advanced CAD processing and workflow integration.

The key libraries for this integration are ezdxf for DXF processing, ODA File Converter for DWG support, and ifcopenshell for IFC/BIM processing. Additional libraries for PDF and document processing will provide comprehensive support for architectural and engineering documentation.

The testing strategy ensures that all components work correctly individually and together, with a focus on handling different file types, integration with the workflow engine, and performance with large files.

By following this implementation plan, the PA-CHECK-MM system will gain robust capabilities for reading, analyzing, and processing architectural/engineering CAD files, plans, and specifications, enhancing its utility for workflow automation in the architecture, engineering, and construction domains.
