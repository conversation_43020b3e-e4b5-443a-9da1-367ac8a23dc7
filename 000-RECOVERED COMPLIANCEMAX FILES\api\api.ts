/**
 * ComplianceMax V74 - API Client
 * ===============================
 * TypeScript client for connecting Next.js frontend to FastAPI backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// ================= Types =================

export interface ChecklistItem {
  id: string;
  title?: string;
  description?: string;
  category?: string;
  phase?: number;
  process_phase?: string;
  pappg_version?: string;
  trigger_condition_if?: string;
  action_required_then?: string;
  grok_tags?: string[];
  status?: string;
}

export interface ComplianceStats {
  total_items: number;
  by_category: Record<string, number>;
  by_phase: Record<string, number>;
  conditional_logic_items: number;
  last_updated: string;
}

export interface HealthStatus {
  status: string;
  timestamp: string;
  version: string;
  database: string;
  services: Record<string, string>;
}

export interface FEMACategory {
  code: string;
  name: string;
  description: string;
}

// ================= API Client Class =================

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(`API Error ${response.status}: ${errorData.error || response.statusText}`);
    }

    return response.json();
  }

  // ================= Health & Status =================

  async getHealth(): Promise<HealthStatus> {
    return this.request<HealthStatus>('/health');
  }

  async getApiInfo(): Promise<Record<string, string>> {
    return this.request<Record<string, string>>('/');
  }

  // ================= Compliance Data =================

  async getComplianceStats(): Promise<ComplianceStats> {
    return this.request<ComplianceStats>('/api/v1/checklist/stats');
  }

  async getChecklistItems(params: {
    limit?: number;
    offset?: number;
    category?: string;
    phase?: number;
    search?: string;
  } = {}): Promise<ChecklistItem[]> {
    const searchParams = new URLSearchParams();
    
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.offset) searchParams.append('offset', params.offset.toString());
    if (params.category) searchParams.append('category', params.category);
    if (params.phase) searchParams.append('phase', params.phase.toString());
    if (params.search) searchParams.append('search', params.search);

    const endpoint = `/api/v1/checklist/items${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return this.request<ChecklistItem[]>(endpoint);
  }

  async getFEMACategories(): Promise<{ categories: FEMACategory[] }> {
    return this.request<{ categories: FEMACategory[] }>('/api/v1/checklist/categories');
  }
}

// ================= Singleton Instance =================

export const apiClient = new ApiClient();

// ================= React Hooks =================

import { useState, useEffect } from 'react';

export function useApiHealth() {
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchHealth() {
      try {
        setLoading(true);
        const healthData = await apiClient.getHealth();
        setHealth(healthData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setHealth(null);
      } finally {
        setLoading(false);
      }
    }

    fetchHealth();
  }, []);

  return { health, loading, error };
}

export function useComplianceStats() {
  const [stats, setStats] = useState<ComplianceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchStats() {
      try {
        setLoading(true);
        const statsData = await apiClient.getComplianceStats();
        setStats(statsData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setStats(null);
      } finally {
        setLoading(false);
      }
    }

    fetchStats();
  }, []);

  return { stats, loading, error, refetch: () => fetchStats() };
}

export function useChecklistItems(params: {
  limit?: number;
  offset?: number;
  category?: string;
  phase?: number;
  search?: string;
} = {}) {
  const [items, setItems] = useState<ChecklistItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchItems() {
      try {
        setLoading(true);
        const itemsData = await apiClient.getChecklistItems(params);
        setItems(itemsData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setItems([]);
      } finally {
        setLoading(false);
      }
    }

    fetchItems();
  }, [params.limit, params.offset, params.category, params.phase, params.search]);

  return { items, loading, error };
}

// ================= Utility Functions =================

export function formatApiError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return 'An unknown error occurred';
}

export function isApiAvailable(): Promise<boolean> {
  return apiClient.getHealth()
    .then(() => true)
    .catch(() => false);
}

export default apiClient; 