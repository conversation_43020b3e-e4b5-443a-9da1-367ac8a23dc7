# [TASK-API-001] Authentication & Onboarding API Development

**Owner**: @assistant  
**Status**: Proposed → In Progress  
**Priority**: High  
**ETA**: 2025-01-16  
**Project Policy Compliance**: Sections 2.1, 4, 5

## Description

Build comprehensive API layer for user authentication and intelligent onboarding that leverages our sophisticated PAPPG logic backend. This enables the professional frontend architecture with proper authentication flow and connects users to our conditional compliance workflows.

## Status History

| Timestamp | Event Type | From Status | To Status | Details | User |
|-----------|------------|-------------|-----------|---------|------|
| 2025-01-15 18:30:00 | Created | N/A | Proposed | Task created based on frontend analysis | @assistant |

## Requirements

### Functional Requirements
1. **User Authentication**
   - JWT-based authentication system
   - User registration with email/password
   - Secure login with session management
   - Password reset functionality

2. **FEMA Integration** 
   - FEMA registration number validation
   - Disaster number (DR-XXXX) validation
   - User profile management

3. **FEMA API Integration & Smart Population**
   - FEMA# lookup with auto-population of user data
   - State → County → DR# hierarchical selection
   - Real-time open disasters scraping from FEMA APIs
   - Auto-population after FEMA# entry
   - Current/eligible disasters by state/county lookup

4. **Intelligent Backend Integration**
   - Auto-trigger PAPPG version determination
   - Incident date capture and processing
   - Conditional logic initialization

### Non-Functional Requirements
- Response time < 500ms for authentication
- 99.9% uptime for critical auth endpoints
- GDPR/privacy compliance for user data
- Comprehensive logging and monitoring

## Implementation Plan

### Phase 1: Authentication Core (Day 1)
```javascript
// API Endpoints to build
POST /api/auth/register
POST /api/auth/login  
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
PUT  /api/auth/profile
```

### Phase 2: FEMA API Integration & Smart Population (Day 2)
```javascript
// FEMA# lookup and auto-population
POST /api/fema/lookup-registration  // Auto-populates user data from FEMA#
GET  /api/fema/user-profile/:femaNumber

// Real-time disaster data (scraped from FEMA APIs)
GET  /api/fema/disasters/open       // Current open disasters
GET  /api/fema/disasters/state/:state  // Open disasters by state
GET  /api/fema/disasters/county/:state/:county  // Filtered by county
GET  /api/fema/disaster/:drNumber   // Specific disaster details

// Hierarchical selection support
GET  /api/geo/states               // All states
GET  /api/geo/counties/:state      // Counties in state
POST /api/fema/user-incident       // Associate user with specific DR#
```

### Phase 3: Intelligent Onboarding (Day 3)
```javascript
// Backend integration endpoints
POST /api/compliance/initialize-project
GET  /api/compliance/pappg-version/:incidentDate
GET  /api/compliance/applicable-policies
GET  /api/compliance/workflow/:projectId
```

## Test Plan

### Testing Strategy (Following Policy Section 5.2)
**Focus**: Integration tests for API endpoints with real database, unit tests for business logic

### Unit Tests
```javascript
describe('PAPPG Version API Integration', () => {
  test('should determine correct PAPPG version for incident date', () => {
    const result = determinePAPPGForAPI('2023-09-15');
    expect(result).toEqual({
      version: 'PAPPG v4.0 (FP 104-009-2)',
      effectiveDate: '2020-06-01',
      endDate: '2025-01-06'
    });
  });
});
```

### Integration Tests
```javascript
describe('Authentication API', () => {
  test('POST /api/auth/register should create user and return JWT', async () => {
    const response = await request(app)
      .post('/api/auth/register')
      .send({ email: '<EMAIL>', password: 'secure123' });
    
    expect(response.status).toBe(201);
    expect(response.body.token).toBeValidJWT();
  });
});
```

### Database Tests
```javascript
describe('User Data Persistence', () => {
  test('should store FEMA registration and incident data', async () => {
    const user = await createTestUser();
    await user.addFEMARegistration('12345', 'DR-4701');
    
    expect(user.femaRegistration).toBe('12345');
    expect(user.disasters).toContainDR('DR-4701');
  });
});
```

## Verification

### Success Criteria
- [ ] All authentication endpoints return proper JWT tokens
- [ ] FEMA registration validation works with real data
- [ ] County/state disaster lookup returns accurate historical data
- [ ] PAPPG version determination integrates with backend logic
- [ ] User profile stores and retrieves FEMA data correctly
- [ ] API response times < 500ms under load
- [ ] 100% test coverage for authentication logic
- [ ] Integration tests pass with PostgreSQL test database

### API Testing Checklist
- [ ] Postman collection created for all endpoints
- [ ] Authentication flow tested end-to-end
- [ ] Error handling for invalid FEMA numbers
- [ ] Rate limiting implemented and tested
- [ ] CORS configured for frontend integration
- [ ] API documentation generated (OpenAPI/Swagger)

## Files Modified

### New Files to Create:
- `SRC/api/auth/` - Authentication endpoints
- `SRC/api/fema/` - FEMA integration endpoints  
- `SRC/api/compliance/` - Backend integration endpoints
- `SRC/middleware/auth.js` - JWT middleware
- `SRC/utils/fema-validation.js` - FEMA data validation
- `TESTS/src/integration/api/` - API integration tests
- `TESTS/src/unit/api/` - API unit tests

### Modified Files:
- `SRC/database/schema.sql` - Add users, sessions tables
- `package.json` - Add Express, JWT, validation dependencies
- `TESTS/jest.config.js` - Add API test configuration

## Dependencies

### Technical Dependencies
```json
{
  "express": "^4.18.0",
  "jsonwebtoken": "^9.0.0", 
  "bcryptjs": "^2.4.3",
  "joi": "^17.9.0",
  "express-rate-limit": "^6.7.0",
  "cors": "^2.8.5"
}
```

### Backend Integration
- Existing PAPPG logic in `phase1-integration-update.js`
- PostgreSQL database with compliance schema
- Document processing pipeline (future integration)

## Risk Assessment

### High Priority Risks
1. **Security**: JWT token management and user data protection
2. **Performance**: Database query optimization for disaster lookups  
3. **Integration**: Connecting to existing backend logic seamlessly

### Mitigation Strategies
- Comprehensive security testing with penetration testing tools
- Database indexing and query optimization
- Extensive integration testing with existing backend components

## Documentation Updates Required

### CHANGELOG.md Updates
```markdown
### Added
- Authentication API with JWT-based security
- FEMA registration validation endpoints
- County/state disaster lookup functionality
- Intelligent PAPPG version determination API
- User profile management with incident data
```

### README Updates
- API endpoint documentation
- Authentication flow documentation
- Integration with existing backend components

## Next Steps After Completion

1. **Frontend Integration** - Connect professional UI to these APIs
2. **Advanced Features** - Document processing API integration
3. **Real-time Updates** - WebSocket for compliance progress
4. **Analytics** - Usage tracking and compliance metrics

---

**This task bridges your sophisticated backend with the professional frontend vision, following test-first development and proper documentation standards.** 