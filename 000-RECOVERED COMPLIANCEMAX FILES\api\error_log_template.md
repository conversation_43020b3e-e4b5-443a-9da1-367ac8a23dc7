# ComplianceMax Error Log Template

## Error Details

### Basic Information
- **Date and Time**: [YYYY-MM-DD HH:MM]
- **Platform**: [Windsurf / Cursor / Replit]
- **Operating System**: [Windows / macOS / Linux + version]
- **Browser** (if applicable): [Chrome / Firefox / Safari + version]

### Error Description
- **Summary**: [Brief one-line description of the error]
- **File/Component**: [Path to file or component name where error occurred]
- **Error Message**: 
```
[Paste the exact error message here]
```

### Context
- **What were you trying to do?**: [Brief description of the action being attempted]
- **Steps to Reproduce**:
  1. [Step 1]
  2. [Step 2]
  3. [Step 3]
  ...

### Environment Information
- **Node.js Version** (if applicable): [e.g., v18.12.1]
- **Python Version** (if applicable): [e.g., 3.11.8]
- **Package Versions** (key packages only):
  - [Package name]: [version]
  - [Package name]: [version]

### Additional Information
- **Screenshots**: [Attach if available]
- **Network Issues**: [Any relevant network/API call failures]
- **Recent Changes**: [Any recent code or environment changes that might be relevant]

---

## Resolution Tracking

### Attempted Solutions
1. **[Date]**: [Solution attempted]
   - **Result**: [Success/Failure/Partial]
   - **Notes**: [Any observations]

2. **[Date]**: [Solution attempted]
   - **Result**: [Success/Failure/Partial]
   - **Notes**: [Any observations]

### Final Resolution
- **Status**: [Resolved / Workaround / Unresolved]
- **Date Resolved**: [YYYY-MM-DD]
- **Solution**: [Brief description of the solution]
- **Code Changes**: [Link to commit or PR if applicable]
- **Prevention**: [Steps taken to prevent similar issues in the future]

---

## Cross-Platform Notes

### Platform-Specific Details
- **Windsurf**: [Any notes specific to Windsurf]
- **Cursor**: [Any notes specific to Cursor]
- **Replit**: [Any notes specific to Replit]

### Environment Variables
- [List any environment variables that differ between platforms] 