'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  useApiHealth, 
  useComplianceStats, 
  useChecklistItems,
  type ChecklistItem,
  type ComplianceStats,
  type HealthStatus
} from '@/lib/api';
import { 
  Activity, 
  AlertCircle, 
  CheckCircle2, 
  Database, 
  FileText, 
  RefreshCw,
  Search,
  Server,
  TrendingUp
} from 'lucide-react';

// ================= Status Components =================

function ApiStatus({ health, loading, error }: { 
  health: HealthStatus | null; 
  loading: boolean; 
  error: string | null; 
}) {
  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API Status</CardTitle>
          <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">Connecting...</div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API Status</CardTitle>
          <AlertCircle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">Offline</div>
          <p className="text-xs text-muted-foreground mt-1">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (health) {
    const isHealthy = health.status === 'healthy';
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API Status</CardTitle>
          {isHealthy ? (
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${isHealthy ? 'text-green-600' : 'text-yellow-600'}`}>
            {isHealthy ? 'Online' : 'Degraded'}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            v{health.version} • {health.database}
          </p>
          <div className="flex gap-1 mt-2">
            {Object.entries(health.services).map(([service, status]) => (
              <Badge 
                key={service} 
                variant={status.includes('operational') || status.includes('connected') ? 'default' : 'destructive'}
                className="text-xs"
              >
                {service}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return null;
}

function StatsCard({ title, value, description, icon: Icon, trend }: {
  title: string;
  value: string | number;
  description?: string;
  icon: any;
  trend?: string;
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value.toLocaleString()}</div>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
        {trend && (
          <div className="flex items-center gap-1 mt-1">
            <TrendingUp className="h-3 w-3 text-green-500" />
            <span className="text-xs text-green-600">{trend}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function CategoryBreakdown({ stats }: { stats: ComplianceStats }) {
  const categories = Object.entries(stats.by_category).sort(([,a], [,b]) => b - a);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>FEMA Categories</CardTitle>
        <CardDescription>Breakdown by compliance category</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {categories.map(([category, count]) => (
            <div key={category} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500" />
                <span className="text-sm font-medium">{category}</span>
              </div>
              <Badge variant="secondary">{count}</Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function PhaseBreakdown({ stats }: { stats: ComplianceStats }) {
  const phases = Object.entries(stats.by_phase).sort(([,a], [,b]) => b - a);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Process Phases</CardTitle>
        <CardDescription>Breakdown by compliance phase</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {phases.slice(0, 8).map(([phase, count]) => (
            <div key={phase} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500" />
                <span className="text-sm font-medium truncate">{phase}</span>
              </div>
              <Badge variant="secondary">{count}</Badge>
            </div>
          ))}
          {phases.length > 8 && (
            <div className="text-xs text-muted-foreground text-center">
              +{phases.length - 8} more phases
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

function RecentItems({ items }: { items: ChecklistItem[] }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Compliance Items</CardTitle>
        <CardDescription>Latest processed checklist items</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.slice(0, 5).map((item) => (
            <div key={item.id} className="flex items-start gap-3 p-3 rounded-lg border">
              <FileText className="h-4 w-4 mt-1 text-muted-foreground" />
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm truncate">{item.title || 'Untitled'}</h4>
                <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                  {item.description || 'No description available'}
                </p>
                <div className="flex gap-1 mt-2">
                  {item.category && (
                    <Badge variant="outline" className="text-xs">{item.category}</Badge>
                  )}
                  {item.pappg_version && (
                    <Badge variant="outline" className="text-xs">{item.pappg_version}</Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// ================= Main Dashboard Component =================

export default function ComplianceDashboard() {
  const { health, loading: healthLoading, error: healthError } = useApiHealth();
  const { stats, loading: statsLoading, error: statsError, refetch: refetchStats } = useComplianceStats();
  const { items, loading: itemsLoading, error: itemsError } = useChecklistItems({ limit: 10 });

  const [searchTerm, setSearchTerm] = useState('');

  const handleRefresh = () => {
    refetchStats();
    window.location.reload(); // Simple refresh for now
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">ComplianceMax V74</h1>
          <p className="text-muted-foreground">FEMA Public Assistance Compliance Dashboard</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Status Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <ApiStatus health={health} loading={healthLoading} error={healthError} />
        
        {statsLoading ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Loading...</div>
            </CardContent>
          </Card>
        ) : statsError ? (
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">Error</div>
              <p className="text-xs text-muted-foreground">{statsError}</p>
            </CardContent>
          </Card>
        ) : stats ? (
          <>
            <StatsCard 
              title="Total Items" 
              value={stats.total_items} 
              description="Compliance checklist items"
              icon={FileText}
            />
            <StatsCard 
              title="Categories" 
              value={Object.keys(stats.by_category).length} 
              description="FEMA compliance categories"
              icon={Database}
            />
            <StatsCard 
              title="Conditional Logic" 
              value={stats.conditional_logic_items} 
              description="Items with IF-THEN rules"
              icon={Activity}
            />
          </>
        ) : null}
      </div>

      {/* Search Bar */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search compliance items..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Data Visualization */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Category Breakdown */}
        {stats && <CategoryBreakdown stats={stats} />}
        
        {/* Phase Breakdown */}
        {stats && <PhaseBreakdown stats={stats} />}
        
        {/* Recent Items */}
        {items.length > 0 && <RecentItems items={items} />}
        
        {/* Error States */}
        {statsError && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Statistics Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{statsError}</p>
            </CardContent>
          </Card>
        )}
        
        {itemsError && (
          <Card>
            <CardHeader>
              <CardTitle className="text-red-600">Items Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">{itemsError}</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Backend Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Backend Connection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <h4 className="font-medium mb-2">API Health</h4>
              {healthLoading ? (
                <Badge variant="secondary">Checking...</Badge>
              ) : health ? (
                <Badge variant={health.status === 'healthy' ? 'default' : 'destructive'}>
                  {health.status}
                </Badge>
              ) : (
                <Badge variant="destructive">Offline</Badge>
              )}
            </div>
            <div>
              <h4 className="font-medium mb-2">Data Load</h4>
              {statsLoading ? (
                <Badge variant="secondary">Loading...</Badge>
              ) : stats ? (
                <Badge variant="default">
                  {stats.total_items} items loaded
                </Badge>
              ) : (
                <Badge variant="destructive">Failed</Badge>
              )}
            </div>
            <div>
              <h4 className="font-medium mb-2">Last Updated</h4>
              <p className="text-sm text-muted-foreground">
                {stats ? new Date(stats.last_updated).toLocaleString() : 'N/A'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 