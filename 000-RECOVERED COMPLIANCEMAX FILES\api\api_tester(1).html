<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ComplianceMax API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        button {
            padding: 8px 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
        }
        button:hover {
            background-color: #2980b9;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        .loading {
            color: #f39c12;
        }
        .endpoint-list {
            margin-top: 15px;
        }
        .tab-container {
            display: flex;
            flex-direction: column;
        }
        .tab-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 10px;
        }
        .service-status {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .status-item {
            flex: 1;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status-active {
            background-color: #d5f5e3;
            border: 1px solid #2ecc71;
        }
        .status-inactive {
            background-color: #fadbd8;
            border: 1px solid #e74c3c;
        }
    </style>
</head>
<body>
    <h1>ComplianceMax API Tester</h1>
    <p>Use this page to test the standalone backend and main application APIs</p>

    <div class="service-status">
        <div id="standalone-status" class="status-item status-inactive">
            <h3>Standalone Backend</h3>
            <p>(Port 8000)</p>
            <div id="standalone-status-text">Checking...</div>
        </div>
        <div id="main-status" class="status-item status-inactive">
            <h3>Main Application</h3>
            <p>(Port 8001)</p>
            <div id="main-status-text">Checking...</div>
        </div>
    </div>

    <div class="container">
        <div class="panel">
            <h2>Standalone Backend</h2>
            <p>Test endpoints on the standalone backend (Port 8000)</p>
            
            <div class="endpoint-list">
                <button onclick="testEndpoint('http://localhost:8000/api/v1/health', 'standalone-result')">Health Check</button>
                <button onclick="testEndpoint('http://localhost:8000/api/v1/auth/me', 'standalone-result')">User Data</button>
                <button onclick="testEndpoint('http://localhost:8000/api/v1/compliance/standards', 'standalone-result')">Standards</button>
                <button onclick="testEndpoint('http://localhost:8000/docs', 'standalone-result')">API Docs</button>
                <button onclick="testEndpoint('http://localhost:8000/', 'standalone-result')">Root Endpoint</button>
            </div>
            
            <h3>Response:</h3>
            <pre id="standalone-result">Click a button above to test an endpoint</pre>
        </div>
        
        <div class="panel">
            <h2>Main Application</h2>
            <p>Test endpoints on the main application (Port 8001)</p>
            
            <div class="endpoint-list">
                <button onclick="testEndpoint('http://localhost:8001/api/v1/health', 'main-result')">Health Check</button>
                <button onclick="testEndpoint('http://localhost:8001/api/v1/auth/me', 'main-result')">User Data</button>
                <button onclick="testEndpoint('http://localhost:8001/docs', 'main-result')">API Docs</button>
                <button onclick="testEndpoint('http://localhost:8001/', 'main-result')">Root Endpoint</button>
            </div>
            
            <h3>Response:</h3>
            <pre id="main-result">Click a button above to test an endpoint</pre>
        </div>
    </div>

    <div class="panel" style="margin-top: 20px;">
        <h2>Troubleshooting Tips</h2>
        <ul>
            <li>Make sure both servers are running using the <code>python run.py</code> command</li>
            <li>Check the terminal windows for any error messages</li>
            <li>If endpoints return 500 errors, check server logs for more details</li>
            <li>For authentication errors, you may need to register/login first</li>
            <li>CORS issues may occur if trying to access from different domains</li>
        </ul>
    </div>

    <script>
        // Check service status on page load
        window.onload = function() {
            checkServiceStatus('http://localhost:8000/api/v1/health', 'standalone');
            checkServiceStatus('http://localhost:8001/api/v1/health', 'main');
        };

        function checkServiceStatus(url, serviceType) {
            fetch(url, { method: 'GET' })
                .then(response => {
                    if (response.status === 200) {
                        document.getElementById(`${serviceType}-status`).className = 'status-item status-active';
                        document.getElementById(`${serviceType}-status-text`).innerHTML = 'ONLINE';
                    } else {
                        throw new Error(`Status: ${response.status}`);
                    }
                })
                .catch(error => {
                    document.getElementById(`${serviceType}-status`).className = 'status-item status-inactive';
                    document.getElementById(`${serviceType}-status-text`).innerHTML = 'OFFLINE';
                });
        }

        async function testEndpoint(url, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.className = 'loading';
            resultElement.textContent = 'Loading...';

            try {
                const startTime = new Date().getTime();
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const endTime = new Date().getTime();
                const responseTime = endTime - startTime;

                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = { message: 'Non-JSON response received' };
                }

                const statusInfo = response.ok ? 'success' : 'error';
                resultElement.className = statusInfo;
                resultElement.textContent = `Status: ${response.status} (${responseTime}ms)\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultElement.className = 'error';
                resultElement.textContent = `Error: ${error.message}\n\nPossible causes:\n- Server is not running\n- CORS policy blocking request\n- Network error`;
            }
        }
    </script>
</body>
</html> 