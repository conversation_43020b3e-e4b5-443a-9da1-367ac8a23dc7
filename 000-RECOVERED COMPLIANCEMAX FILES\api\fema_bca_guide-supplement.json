{"filename": "fema_bca_guide-supplement.pdf", "pages": [{"page_number": 1, "text": "Supplement to the\nBenefit-Cost Analysis\nReference Guide\nJune 2011\nFederal Emergency Management Agency\nDepartment of Homeland Security\n500 C Street, SW\nWashington, DC 20472"}, {"page_number": 2, "text": "This document was prepared by\nURS Group, Inc.\n200 Orchard Ridge Drive, Suite 101\nGaithersburg, MD 20878\nContract No. HSFEHQ-06-D-0162\nTask Order HSFEHQ-09-J-0024\nACKNOWLEDGEMENT<PERSON> (FEMA HQ)\n<PERSON><PERSON> (URS Group, Inc.)\n<PERSON> (Atkins)\n<PERSON> (URS Group, Inc.)\n<PERSON> (URS Group, Inc.)\nJae Park (URS Group, Inc.)\n<PERSON> (URS Group, Inc.)\n<PERSON> (URS Group, Inc.)\n<PERSON> (Atkins)\n<PERSON> (Atkins)\n<PERSON> (URS Group, Inc.)"}, {"page_number": 3, "text": "Table of Contents\nACRONYMS AND ABBREVIATIONS .................................................................................................. v\nSECTION ONE INTRODUCTION ............................................................................................... 1-1\n1.1 Purpose................................................................................................................. 1-1\n1.2 Cost-Effectiveness Analysis Requirements for Hazard Mitigation\nAssistance Program Funds ................................................................................... 1-1\nSECTION TWO FLOOD HAZARD MITIGATION .......................................................................... 2-1\n2.1 Working with the Damage Frequency Assessment Module ................................ 2-3\n2.1.1 Documenting Expected, Historical, and Residual Damages ................... 2-3\n2.1.2 Determining Recurrence Intervals ........................................................... 2-4\n2.1.2.1 Step 1 − Determine Gage Period of Record.............................. 2-4\n2.1.2.2 Step 2 − Retrieve and Download the Annual Peak\nDischarge for the Event of Interest ........................................... 2-7\n2.1.2.3 Step 3 − Import Annual Peak Data into PeakFQ Program\nto Identify Exceedance Probability and Return Interval ........... 2-8\n******* Calculating Peak Daily Discharges Using Mean Daily\nDischarges ............................................................................... 2-11\n2.1.3 Adjusting Analysis Duration.................................................................. 2-14\n2.1.3.1 Acceptable Documentation ..................................................... 2-15\n2.1.3.2 Entering the Analysis Duration in the DFA Module .............. 2-17\n2.1.4 Using National Flood Insurance Program BureauNet Data................... 2-21\n2.1.4.1 Access to BureauNet ............................................................... 2-21\n2.1.4.2 Flood Insurance Coverage ...................................................... 2-21\n2.1.4.3 Estimated Flood Damages versus Actual Claims\nPayments ................................................................................. 2-22\n2.1.4.4 Interpreting BureauNet Claims Data....................................... 2-22\n2.1.4.5 How to Use NFIP BureauNet Data with the DFA Module .... 2-25\n2.1.4.6 Enter NFIP Data into DFA Module and Determine BCR ...... 2-25\n2.1.4.7 Using the BCA Full Data Flood Module to Maximize\nBenefits in the DFA Module ................................................... 2-30\n2.2 Working with the Flood Module........................................................................ 2-38\n2.2.1 Counting Damages for Finished or Unfinished Basement .................... 2-39\n2.2.1.1 Unfinished Basements............................................................. 2-39\n2.2.1.2 Finished Basement with Window Elevation Above or\nBelow the Adjacent Ground Elevation ................................... 2-40\n******* Finished Basement with Walkout or a Garage Elevation\nEqual to the Adjacent Ground................................................. 2-43\n2.2.1.4 Partially Finished Basement ................................................... 2-44\n2.2.2 Using Topographic Maps with 2-Foot Contour Intervals to\nEstimate First Floor Elevations.............................................................. 2-47\n2.2.2.1 Topographic Map Features ..................................................... 2-47\n2.2.2.2 Contour Interval ...................................................................... 2-49\n2.2.2.3 Estimating the First Floor Elevation ....................................... 2-49\ni"}, {"page_number": 4, "text": "Table of Contents\n2.2.3 Other Losses and Non-Traditional Benefits .......................................... 2-53\n2.2.3.1 Damage Estimates for Other Losses and Non-Traditional\nBenefits ................................................................................... 2-53\n2.2.3.2 Acceptable Documentation for Non-Traditional Benefits ...... 2-54\n******* Entering Non-Traditional Benefits Data in the BCA\nFlood Module .......................................................................... 2-56\nSECTION THREE WIND HAZARD MITIGATION ............................................................................. 3-1\n3.1 Deriving Wind Speed Data for the Hurricane Wind Module .............................. 3-1\n3.2 Working with the Tornado Safe Room Module ................................................ 3-10\n3.2.1 Identifying the Target Population .......................................................... 3-10\n3.2.2 Calculating Gross and Usable Area ....................................................... 3-14\n3.2.3 Calculating the Maximum Occupancy................................................... 3-15\n3.2.4 Example: Determining Required Usable Area from Maximum\nOccupancy.............................................................................................. 3-15\nSECTION FOUR INCORPORATING LOSS OF SERVICES FOR CRITICAL PUBLIC\nFACILITIES ....................................................................................................... 4-1\n4.1.1 Accessing the Critical Facility Function with Flood Module .................. 4-4\n4.1.2 Accessing the Critical Facility Function with DFA Module ................... 4-5\n4.1.3 Determining the Number of People Served by a Critical Facility ........... 4-5\n4.1.4 Determining the Distance (in miles) between Critical Facilities ............. 4-8\n4.1.5 Determining the Type of Area Served by Fire and Police Stations ....... 4-10\n4.1.5.1 Fire .......................................................................................... 4-10\n4.1.5.2 Police....................................................................................... 4-10\n4.1.6 Determining the Number of Police Officers Serving the Same Area\nin the Aftermath of a Disaster ................................................................ 4-11\nSECTION FIVE AVAILABLE TECHNOLOGY AIDS ..................................................................... 5-1\n5.1.1 Using Free Layers in Google Earth.......................................................... 5-1\n5.1.1.1 National Flood Hazard Layer.................................................... 5-1\n5.1.1.2 USGS Streamflow Data ............................................................ 5-5\n5.1.1.3 Spatial Calculations................................................................... 5-7\n5.1.2 Using Google Earth Pro License............................................................ 5-11\n5.1.3 Using Hazus for Flood Analysis ............................................................ 5-14\n5.1.3.1 Acceptable Uses of Hazus....................................................... 5-15\n5.1.3.2 Unacceptable Uses of Hazus................................................... 5-22\nSECTION SIX REFERENCES .................................................................................................. 6-1\nii"}, {"page_number": 5, "text": "Table of Contents\nFIGURES\nFigure 2.1: Selecting between the DFA and Flood modules for a flood mitigation project ........ 2-2\nFigure 2.2: Example of an exceedance probability chart........................................................... 2-11\nFigure 2.3: Find exceedance probability of April event ............................................................ 2-14\nFigure 2.4: Sample letter documenting a change in local flow conditions ................................ 2-16\nFigure 2.5: Sample before- and after-development photographs documenting a change in\nland use (<PERSON><PERSON> 2001) .................................................................................... 2-17\nFigure 2.6: Sample NFIP flood claim ........................................................................................ 2-23\nFigure 2.7: FFE of an unfinished basement ............................................................................... 2-40\nFigure 2.8: FFE and DDF offset for finished basement............................................................. 2-41\nFigure 2.9: FFE for a finished walkout basement ...................................................................... 2-43\nFigure 2.10: Partial finished basement, divided into two structures .......................................... 2-45\nFigure 2.11: Example of topographic map (Lake County 2011) ............................................... 2-48\nFigure 2.12: Example of a map scale representations................................................................ 2-49\nFigure 2.13: Example of contour lines and contour interval ..................................................... 2-49\nFigure 2.14: Determining building location on a topographic map ........................................... 2-50\nFigure 2.15: Outline the building footprint (shown in green).................................................... 2-51\nFigure 2.16: Measure the height of the first floor slab .............................................................. 2-52\nFigure 2.17: Measure the height to the first floor slab............................................................... 2-52\nFigure 2.18: Sample insurance claim documenting damage ..................................................... 2-55\nFigure 2.19: Sample of repair records for vehicle damage ........................................................ 2-55\nFigure 2.20: Sample photograph showing vehicle damage due to flooding .............................. 2-56\nFigure 3.1: Sample proposed safe room location with 0.5-mile radius ..................................... 3-11\nFigure 3.2: Sample of U.S. Census Bureau data showing average household size ................... 3-12\nFigure 3.3: Sample of U.S. Census Bureau data showing population 18 years and over .......... 3-13\nFigure 3.4: Example safe room floor plan ................................................................................. 3-16\nFigure 4.1: Hospital example - locations of critical facilities in Montgomery County, MD\nDistrict 3............................................................................................................... 4-7\nFigure 4.2: Hospital example - population for Montgomery County District 3 population ........ 4-7\nFigure 4.3: Hospital example - nearest alternative critical facility .............................................. 4-8\nFigure 4.4: Hospital example - distance from Hospital A to Hospital B ..................................... 4-9\nFigure 5.1: Comparison of NFHL (left) and FEMA Map Service Center FIRMette (right) ....... 5-2\nFigure 5.2: NFHL high altitudes option....................................................................................... 5-3\niii"}, {"page_number": 6, "text": "Table of Contents\nFigure 5.3: NFHL medium altitude areas, zones, and boundaries ............................................... 5-4\nFigure 5.4: Proposed project location on NFHL.......................................................................... 5-5\nFigure 5.5: USGS streamflow gages............................................................................................ 5-6\nFigure 5.6: USGS station number display ................................................................................... 5-7\nFigure 5.7: Google Earth Pro toolbar........................................................................................... 5-7\nFigure 5.8: Proposed detour and distance .................................................................................... 5-9\nFigure 5.9: Using Google Earth Pro to determine 0.50-mile radius from the project\nlocation............................................................................................................... 5-11\nFigure 5.10: Data layers available in Google Earth Pro (U.S.).................................................. 5-11\nFigure 5.11: U.S. demographics using Google Earth Pro .......................................................... 5-12\nFigure 5.12: U.S. parcel data using Google Earth Pro ............................................................... 5-13\nFigure 5.13: Daily traffic counts using Google Earth Pro ......................................................... 5-14\nTABLES\nTable 1: Example of BureauNet NFIP Claims Data .................................................................. 2-25\nTable 2: Example Data Obtained from DFA Module................................................................ 2-30\nTable 3: Example of Approximate Displacement Cost Determination ..................................... 2-35\nTable 4: Example for Residence with Two or More Stories without Basement ....................... 2-43\nTable 5: WSEL and discharge data for 100 Main Street, Louisville, KY from FIS.................. 2-57\nTable 6: Other damages associated with flood depth ................................................................ 2-57\nTable 7: Typical Gross Area Reductions by Room Use ............................................................ 3-14\nTable 8: Maximum Occupant Density for Tornado Community Safe Rooms .......................... 3-15\nTable 9: Applicability of Technology in BCA ............................................................................ 5-1\nTable 10: Hazus MR2 Default Contents Value Based on Percentage of Structure Value ........ 5-18\nTable 11: Hazus MR3 Displacement Costs (2008 Values) ....................................................... 5-20\niv"}, {"page_number": 7, "text": "Acronyms and Abbreviations\nACV Actual Cash Value\nADT Average Daily Traffic\nASCE American Society of Civil Engineers\nAVM Automated Valuation Model\nBCA Benefit-Cost Analysis\nBCR Benefit-Cost Ratio\nBFE Base Flood Elevation\nBRV Building Replacement Value\ncfs Cubic Foot per Second\nDDF Depth-Damage Function\nDDT Data Documentation Template\nDFA Damage Frequency Assessment\nFEMA Federal Emergency Management Agency\nFFE First Floor Elevation\nFIRM Flood Insurance Rate Map\nFIS Flood Insurance Study\nGBS General Building Stock\nGIS Geographic Information System\nH&H Hydrology and Hydraulics\nHAG Highest Adjacent Grade\nHazus Hazards U.S.\nHEC-SSP Hydrologic Engineering Center – Statistical Software Package\nHEC-RAS Hydrologic Engineering Centers River Analysis System\nHMA Hazard Mitigation Assistance\nHMGP Hazard Mitigation Grant Program\nLAG Lowest Adjacent Grade\nLOMA Letter of Map Amendment\nLOMR Letter of Map Revision\nMR2 Major Release 2\nMR3 Major Release 3\nMR4 Major Release 4\nv"}, {"page_number": 8, "text": "Acronyms and Abbreviations\nMR5 Major Release 5\nNAVD North American Vertical Datum\nNFHL National Flood Hazard Layer\nNFIP National Flood Insurance Program\nNGVD National Geodetic Vertical Datum\nOMB Office of Management and Budget\nRCV Replacement Cost Value\nsf square feet\nSFHA Special Flood Hazard Area\nSHMO State Hazard Mitigation Officer\n<PERSON><PERSON> <PERSON><PERSON> of Work\nUDF User-Defined Facilities\nUSACE U.S. Army Corps of Engineers\nUSDA U.S. Department of Agriculture\nUSGS U.S. Geological Survey\nWSEL Water Surface Elevation\nvi"}, {"page_number": 9, "text": "Introduction\nSECTION ONE INTRODUCTION\n1.1 Purpose\nThis guidance document was prepared for the U.S. Department of Homeland Security’s Federal\nEmergency Management Agency (FEMA) under Contract No. HSFEHQ-06-D-0162, Task Order\nHSFEHQ-09-J-0024. The purpose of the guide is to supplement the information available at\nhttp://www.fema.gov/government/grant/bca.shtm regarding the FEMA benefit-cost analysis\n(BCA) software Version 4.5.5 with additional guidance on performing a BCA using the Damage\nFrequency Assessment (DFA) and Flood modules. All references to BCA software modules in\nthis document are for Version 4.5.5. This document explains what constitutes acceptable\ndocumentation for Hazard Mitigation Assistance (HMA) grant subapplications. In addition, it\nexplains additional and alternative methods for determining benefits for some of the most\ncommon benefit categories for buildings (e.g., building damage, contents damage, displacement\nand loss of function) as well as non-traditional benefits (e.g., agricultural equipment and\nlandscaping equipment) not generally discussed in BCA training classes. These methods will\nfacilitate better preparation of a BCA for a HMA Program grant application in accordance with\nthe Fiscal Year 2011 (FY11) Hazard Mitigation Assistance Unified Guidance: Hazard\nMitigation Grant Program, Pre-Disaster Mitigation Program, Flood Mitigation Assistance\nProgram, Repetitive Flood Claims Program, Severe Repetitive Loss Program (FY11 HMA\nUnified Guidance, FEMA 2010a) and recognize additional benefits not previously considered.\nThis document does not replace the formal BCA training offered by FEMA. To schedule\ntraining, Applicants and subapplicants can contact their FEMA Regional office or State Hazard\nMitigation Officer (SHMO).\n1.2 Cost-Effectiveness Analysis Requirements for Hazard Mitigation Assistance\nProgram Funds\nTo be eligible for HMA Program funding, mitigation projects must be cost effective as\ndemonstrated by a FEMA-validated BCA. A BCA evaluates the future benefits (projected losses\navoided) of a project in relation its cost. The BCA evaluation results in a benefit-cost ratio\n(BCR). If the future benefits are equal to or greater than the project cost, then the BCR is equal\nto or greater than 1.0 and the proposed project is considered cost effective. If the benefits are less\nthan the cost, then the BCR is less than 1.0, and the proposed project is not considered cost\neffective. Only projects that demonstrate a BCR of 1.0 or greater are considered for HMA\nfunding.\nFor projects submitted for funding under the Hazard Mitigation Grant Program (HMGP), an\nexpedited cost-effectiveness determination can be made for property acquisition and structure\ndemolition or relocation projects when certain conditions are met. Specifically, for structures\nidentified in a riverine floodway or Special Flood Hazard Area (SFHA) on the current effective\nFlood Insurance Rate Map (FIRM) and declared substantially damaged due to the impacts of\nflooding by a local authority having such jurisdiction, property acquisition and structure\ndemolition or relocation is considered cost effective and a BCA is not required to be submitted\nfor the structure.\n1-1"}, {"page_number": 10, "text": "Flood Hazard Mitigation\nSECTION TWO FLOOD HAZARD MITIGATION\nMitigation projects must reduce the potential future damages to a building and its contents,\ninfrastructure, and human lives to recognize their benefits. Additionally, loss of function—\ndisplacement of occupants from the building and/or loss of public services—may also be reduced\nby mitigation projects, thereby further increasing the benefits of the project. The FEMA BCA\nDFA and Flood modules are designed to analyze riverine and coastal flood sources and compare\nbefore- and after-mitigation damages to buildings and/or infrastructure. Each of these two\nmodules requires different information. The selection of which module to use is based on the\ntype of information available to the user.\nThe DFA module is more flexible than the Flood module, and therefore the most frequently used\nmodule for the HMA Program grant subapplications. The DFA module analyzes proposed\nmitigation projects based on damages (either historical or expected) and future damages avoided.\nThe DFA module is extremely useful for projects in areas where high costs were incurred from\nhistorical flooding events but for which no FEMA Flood Insurance Study (FIS) is available. The\nDFA module is commonly used to analyze stormwater management and drainage improvement\nprojects. It can also be used to analyze much larger projects. The DFA module (Section 2.1) is\nrecommended in place of the Flood module (Section 2.2) when key structural information, such\nas the first floor elevation (FFE), or critical flood hazard resources, such as the FIRM, FIS, or the\nbase flood elevation (BFE), are not available.\nAnother important use for the DFA module is as a secondary analysis method for mitigation\nprojects that do not result in a BCR of 1.0 or more in the Flood module. The DFA module can be\nused to analyze projects with documented historical damage records or hydrology and hydraulics\n(H&H) studies that indicate expected flood damages. If the BCR is less than 1.0 for a project\nanalyzed in the Flood module, but is equal to or greater than 1.0 using the DFA module, the\ncomplete and well-documented DFA module can be submitted in a project subapplication instead\nof the Flood module. The subapplication should explain why the DFA module was used in lieu\nof the Flood module.\nThe Flood module analyzes proposed mitigation projects based on the effect a storm of a certain\nmagnitude will have on buildings before and after the project is implemented. The Flood module\nsoftware is designed for evaluating individual buildings within a project. The Flood module is\nrecommended for BCAs when users have detailed flood hazard information and structural data\nfor a project, including FFEs. The default depth-damage functions (DDFs) in the Flood module\napply to buildings, their contents, and their loss of function and displacement time during and\nafter a flood event. The Flood module provides an accurate calculation of risk and maximum\nbenefits for mitigation projects.\nFigure 2.1 illustrates the decision-making process for selecting between the DFA and Flood\nmodules for flood hazard mitigation projects. Additional information regarding the modules is\npresented in Sections 2.1 and 2.2 and can be obtained at the BCA Web site\nhttp://www.bchelpline.com/BCAToolkit/index.html.\n2-1"}, {"page_number": 11, "text": "Flood Hazard Mitigation\nFigure 2.1: Selecting between the DFA and Flood modules for a flood mitigation project\n2-2"}, {"page_number": 12, "text": "Flood Hazard Mitigation\n2.1 Working with the Damage Frequency Assessment Module\nThe FEMA BCA DFA module is a flexible tool for calculating project benefits and costs for\nproposed mitigation projects when users do not have accurate hazard or structural information.\nThe DFA module was developed to calculate project benefits and costs for proposed hazard\nmitigation projects based on either documented historic damages (such as loss of function or\nphysical damage) from three or more events with unknown recurrence intervals, or expected\ndamages from at least two events of different recurrence intervals. It can be used to analyze a\nvariety of facility types including utilities, roads and bridges, and buildings.\nThis section includes information on the following common issues encountered when using the\nDFA module:\n Documenting Historical, Expected, and Residual Damages (Section 2.1.1)\n Determining Recurrence Intervals (Section 2.1.2)\nThis section provides information for users entering expected damages\n Adjusting Analysis Duration (Section 2.1.3)\nThis section provides information for users entering historical damages\n Using National Flood Insurance Program BureauNet Information (Section 2.1.4)\nThis section provides information for users entering historical damages\n2.1.1 Documenting Expected, Historical, and Residual Damages\nThe main advantage of the DFA module is its flexibility: it can be used for a wide range of\nhazards and mitigation project types. However, since some flooding events are 20 or 30 years\nold, data entered in the DFA module can be difficult to properly document. Historically,\nsubapplications submitted for HMA program grant funding that use the BCA DFA module have\ncommonly used values that are insufficiently documented with credible sources, resulting in\ndelays in project funding. Users should begin by reviewing the suggested sources of\ndocumentation provided in the DFA Data Documentation Template (DDT) that is available from\nthe BCA Helpline Web site (www.bchelpline.com; click the “Resource Kit” tab and the DFA\nicon).\nExpected Damages\nIf historical damage amounts are unknown, engineering calculations can be used to determine\nexpected damages from hazard events. Determining the recurrence intervals of historical hazard\nevents is problematic because the data are often incomplete, inaccurate, or out of date. As a\nresult, many users do not consistently or accurately determine the recurrence intervals of\nhistorical hazard events, which results in incorrect BCRs. Users should consult with an engineer\nor technical expert familiar with the proposed project to obtain cost estimates for damages\nresulting from at least two hazard events with different recurrence intervals. Calculations for at\nleast two hazard events are required for the “Expected Damages” field in the DFA module. The\napplicable sections of the FIS, or the engineering report and H&H analysis must be submitted\nwith the subapplication to document inputs used for expected damages in the DFA module.\n2-3"}, {"page_number": 13, "text": "Flood Hazard Mitigation\nHistorical Damages\nIf historical damages are being used in the DFA module, then at least three events are required,\nand the events must have occurred in different years.\n Historical damage records for residential or non-residential structures may come from\nBureauNet data (see Section 2.1.4 for a detailed explanation) or repair receipts\n Extrapolation outside of known data points is not acceptable\nResidual Damages\nRegardless of whether historical or expected damages are used in the DFA module, the project\nlevel of effectiveness must be considered. Almost all mitigation projects (with the exception of\nacquisition projects) remain subject to damage from possible future hazard events. These\ndamages are called residual damage. Users should consult with the engineer or technical expert\nfamiliar with the project to obtain level of effectiveness.\n2.1.2 Determining Recurrence Intervals\nTo estimate the recurrence interval of a storm event near a project site, it is important to be able\nto retrieve and analyze stream gage data for the closest appropriate stream or river. The U.S.\nGeological Survey (USGS) Water Resources Division is the Federal agency responsible for\nmonitoring water data.\nThis section outlines a process for determining whether stream gage data are available, for\nretrieving the appropriate data, and for creating an exceedance probability chart for estimating\nthe exceedance probability of the storm event. The inverse of the exceedance probability is then\nused to determine the recurrence interval.\nThe process outlined in this section is one of many methods available and should not be\nconsidered the only acceptable method to determine recurrence intervals. For example, the\nrecurrence interval of an event can also be determined from rainfall gage data but requires an\nanalysis beyond the scope of this document. The limitations and acceptable methods are\nexplained in detail in Appendix C of FEMA’s Guidelines and Specifications for Flood Hazard\nMapping Partners (FEMA 2003).\nThe process for using stream gage data is demonstrated using an example storm event that\noccurred in February 1996 at a project site located near the mouth of Issaquah Creek in\nWashington State. The following steps can be adapted to project locations throughout the United\nStates:\n Step 1. Determine if the gage data period of record includes the event of interest\n Step 2. Retrieve and download the annual peak discharge for the event of interest\n Step 3. Import annual peak discharges into the USGS PeakFQ program, output an\nexceedance probability chart for the gage, estimate the exceedance probability of the\nevent (in percent), and take the inverse of the result to determine the recurrence interval\n2.1.2.1 Step 1 − Determine Gage Period of Record\nThe URL below is the USGS homepage for surface water data, where XX is replaced with the\ntwo letter state abbreviation (WA for Washington in the example):\n2-4"}, {"page_number": 14, "text": "Flood Hazard Mitigation\nhttp://waterdata.usgs.gov/XX/nwis/sw\nThe first step is to check whether gage data are available for any streams near the proposed\nproject site and whether the gage data include the storm event of interest.\nExample: Issaquah Creek, WA\nBackground: The project site is near the mouth of Issaquah Creek. The storm event of interest\noccurred in February 1996.\nApproach:\n1. Go to http://waterdata.usgs.gov/wa/nwis/sw.\n2. Click the Daily Data button\n3. Select “Site Name” under the column labeled Site Identifier. The Site Attribute column has\n“Site Type” selected as the default and can be left as is. If desired, users can further narrow\ntheir search by selecting “County” in the Site Location column. Click Submit.\n4. Enter the site name. Users can enter the name of the flooding source or the city where the\nproject is located. Select match any part. If the “County” box was checked in the previous\nstep, choose the county where the project is located. “Site Type” will already be selected.\n2-5"}, {"page_number": 15, "text": "Flood Hazard Mitigation\n5. <PERSON>roll down to the Choose Output Format section and select the Brief descriptions option.\nClick Submit.\n6. The search will display a list of gages matching the criteria entered in the previous steps.\nSelect the most relevant stream data. The example search resulted in 10 gages on Issaquah\nCreek. Three of the 10 gages are shown in the screen shot below. Note that gages can go off-\nline without prior warning, resulting in variations over time in the number of gages found\nthrough the search function.\n2-6"}, {"page_number": 16, "text": "Flood Hazard Mitigation\nExample Problem Results: For the example problem, Gage ID#12121600 is the gage nearest to\nthe project location. It has a period of record of 45 years for peak discharge (from 1963 to 2009),\nand covers the February 1996 event.\nDiscussion\nIf it is unclear which gage is closest to the project location, the latitude and longitude information\nprovided for each gage should be compared to the project location. The proximity of the gage to\nthe project site is important in determining whether flows recorded at the gage represent most or\nall of the flow at the project site. Gages upstream of the project site will not record tributary or\ngroundwater inflows that enter the flooding source between the gage and the project site.\nHowever, if the user’s purpose is to determine the size of an event, a gage upstream or\ndownstream of the project can be used as long as the gage is located on the same flooding source\nand was affected by the same storm events.\nUsers can determine the period of record from the gage data, but it should be noted that the\nshorter the gage data record length, the higher the uncertainty. This means an exceedance\nprobability of 10 percent should be derived from a gage record with a minimum of 10 years of\ndata; however, a longer period of record is desirable, as this is more likely to capture cyclical\nvariation in annual precipitation. Wet and dry years often occur in cycles lasting several years,\nand the longer the record, the more likely the computed exceedance flows will represent typical\nflow conditions.\n2.1.2.2 Step 2 − Retrieve and Download the Annual Peak Discharge for the Event of Interest\nUSGS archives annual peak discharges as well as daily mean discharges for the period of record.\nOften a declared event will also be the annual peak. The simplest way to determine the peak\ndischarge is to retrieve and download the annual peak discharges from the archives, as\ndemonstrated with the Issaquah Creek example.\nExample: Issaquah Creek, WA\nApproach:\n1. Return to the Surface Water homepage, http://waterdata.usgs.gov/wa/nwis/sw\n2. Click on Peak-flow data\n3. Select the option to search by Site Number. Click Submit.\n4. Enter the Gage ID#12121600 and select exact match. Scroll down to Retrieve published peak\nstreamflow data for selected sites and choose Table of data. Click Submit.\n2-7"}, {"page_number": 17, "text": "Flood Hazard Mitigation\n5. Review results. The peak discharge for the event of interest will be listed in the table. In this\ncase, the February 1996 event was the annual peak and the peak discharge was 2,420 cubic\nfeet per second (cfs).\n6. Download the annual peak discharge data by selecting the peakfq (watstore) format button.\n7. Save in .txt format to user’s computer.\nDiscussion\nNote that multiple events could have occurred during the same year and the event of interest may\nnot be largest. If the event of interest is not listed as the annual peak, other estimation methods\nwill need to be used. These methods are discussed in Section *******.\n2.1.2.3 Step 3 − Import Annual Peak Data into PeakFQ Program to Identify Exceedance\nProbability and Return Interval\nThe USGS PeakFQ program can provide estimates of instantaneous annual peak flows for a\nrange of recurrence intervals, including 1.5, 2, 2.33, 5, 10, 25, 50, 100, 200, and 500 years. It\nalso has an output option for an exceedance probability chart. Once an estimate of the\nexceedance probability for the event of interest has been determined, the inverse can be taken to\ndetermine the recurrence interval.\nApproach\nFollow these steps to use the USGS PeakFQ program:\n2-8"}, {"page_number": 18, "text": "Flood Hazard Mitigation\n1. Download and install the free program from http://water.usgs.gov/software/PeakFQ.\n2. Start the program\n3. Choose File→Open\n4. Select the .txt file downloaded in the previous step (Section 2.2.1.2) and click “Open.” This\nwill load the information from the .txt file into the PeakFQ program. The PeakFQ home\nscreen will open. The Station Specifications tab will be selected by default.\n5. Select Output Options to configure the output options. Select the checkboxes for Print\nPlotting Positions and Line Printer Plots. Choose BMP as the Graphic Plot Format. Click\nRun PEAKFQ to generate results.\n2-9"}, {"page_number": 19, "text": "Flood Hazard Mitigation\n6. Select the Results tab. An output file with flows corresponding to each recurrence interval\nwill be listed under Output File. Click View to view the file. To view an exceedance\nprobability chart for the gage period of record, select the gage number under Graphs and then\nView.\nUsing Exceedance Probability Charts to Obtain Return Intervals\nFor the purpose of this calculation, exceedance probability is approximately the inverse of the\nrecurrence interval. The exceedance probability chart (Figure 2.2) gives exceedance probability\n(in percent) on the x-axis, and the discharge (in cfs) using a logarithmic scale on the y-axis. On\nthe logarithmic, scale 102 is equal to 100 cfs, 103 is equal to 1,000 cfs, and 104 equals 10,000 cfs.\nThe exceedance probability is obtained by reading horizontally across from the y-axis until the\ndata points are reached, and then reading vertically down.\nThe peak discharge for the Issaquah Creek, WA, February 1996 event was 2,420 cfs, According\nto this exceedance probability chart, this event had an annual exceedance probability of\napproximately 20 percent. Since exceedance probability is the inverse of recurrence interval, this\nwas a 5-year event.\nUsers can also use the U.S. Army Corps of Engineers (USACE) Hydrologic Engineering Center\n– Statistical Software Package (HEC-SSP) to generate exceedance probability charts. This\nfreeware program can be downloaded from http://www.hec.usace.army.mil/software/hec-ssp/.\nThe advantage of HEC-SSP is that it includes an option to upload the annual peaks through a\nMicrosoft Excel file, meaning estimated/unpublished peaks can be added by hand. The\ndisadvantage is that it is more complicated than PeakFQ and may require engineering expertise\nand judgment.\n2-10"}, {"page_number": 20, "text": "Flood Hazard Mitigation\nFigure 2.2: Example of an exceedance probability chart\n******* Calculating Peak Daily Discharges Using Mean Daily Discharges\nUsers should be aware of the limitations of the process described in Sections 2.2.1.1 to *******.\nThere is an approximate 6-month lag time for the annual peak discharges published by USGS\n(i.e., an annual peak for 2010 would not be published until mid-2011). If the event occurred\nrecently and there is no published peak for that event, the mean daily discharge will need to be\nscaled up using a daily mean-to-maximum ratio as shown below.\nIf the event of interest was not the peak for that year or if the peak has not yet been published,\nmean daily discharges will need to be retrieved and adjusted using a ratio to determine the peak\ndaily discharge of the event. The peak daily discharge would then be used to find the exceedance\nprobability on the exceedance probability chart (Figure 2.2).\nExample: Issaquah Creek, WA\nFind the recurrence interval of the event that occurred on April 24, 1996. This event will not\nregister as the annual maximum for 1996 because the February event was much larger.\nTherefore, the peak daily discharge must be used to determine the exceedance probability.\nApproach:\nObtain the mean daily discharge for the event as follows.\n1. Select the checkbox for the appropriate gage.\n2-11"}, {"page_number": 21, "text": "Flood Hazard Mitigation\n2. Leave all other fields blank and scroll down to the Retrieve USGS Surface-Water Daily Data\nfor Selected Sites section. Under Retrieve data for select that data range option and type in\nthe appropriate range.\n3. The event of interest is known to have occurred in the second half of the month of April (see\nbelow where a data range of 1996-04-15 to 1996-04-30 is entered). Choose Table of data\nunder output options and click Submit.\n4. Find the mean daily discharge in the table.\n2-12"}, {"page_number": 22, "text": "Flood Hazard Mitigation\n5. Repeat Steps 2 and 3 to determine the mean daily discharge corresponding to the annual peak\nevent that occurred in February.\n6. Estimate the peak daily discharge by multiplying the mean daily discharge by the mean-to-\npeak ratio (Equation 1).\nEquation 1 Q =Q A /A\nmax avg max avg\nwhere: Q is the peak daily discharge for the event of interest\nmax\nQ is the mean daily discharge for the event of interest\navg\nA is the annual peak discharge for that year\nmax\nA is the mean daily discharge corresponding to the annual peak discharge for\navg\nthat year\nThe peak daily discharge for the April event is calculated as follows:\nQ =(209)(2,420)/(1,870)=270 cfs\nmax\nExample Results\nThe exceedance probability of the event is then found using the exceedance probability chart\ncreated by PeakFQ in Section *******. Using the data in Figure 2.3, the exceedance probability of\nthe April event is 99.5 percent. The inverse of 99.5 percent is 1; therefore, the recurrence interval\nfor the April event is 1 year.\n2-13"}, {"page_number": 23, "text": "Flood Hazard Mitigation\nFigure 2.3: Find exceedance probability of April event\n2.1.3 Adjusting Analysis Duration\nWhen the flood recurrence interval of a hazard event is unknown, it can be estimated using an\nunknown frequency calculator integrated in the DFA module; required inputs to the calculator\nare damage values, event years, and year built or analysis duration. An analysis duration value\ncan be used in lieu of the year built if it is unknown or the project area has experienced\nsignificant changes. For example, a project includes a bridge that was reconstructed for larger\ncarrying capacity long ago, and the town does not know exactly when the bridge was built and\nhas experienced flooding recent years. It is acceptable to use the analysis duration rather than the\nyear built for the unknown frequency calculator. The analysis duration is defined as the number\nof years that records of damage, loss, or hazard level (e.g., flood elevation, wind speed) are\nobserved for more than 10 years. For structures that are more than 10 years old, the analysis\nduration is typically the age of the structure. For structures that are less than 10 years old or\nwhen a change in local flow conditions occurred less than 10 years earlier, the analysis duration\nis 10 years.\nWhen local flow conditions have changed significantly during the life of a structure, it may be\nappropriate to adjust the analysis duration. To address this situation, an alternate means of\ndetermining the analysis duration has been established, and the analysis duration value may be\nadjusted in the DFA module. For results from user-entered analysis duration in a grant\n2-14"}, {"page_number": 24, "text": "Flood Hazard Mitigation\nsubapplication, acceptable documentation of a significant change in flow conditions must be\nincluded in the subapplication to support the adjustment of the analysis duration. If the user can\nsatisfy this requirement, the analysis duration can be assumed to begin on the date the change\nfirst occurred if the analysis duration exceeds 10 years.\n2.1.3.1 Acceptable Documentation\nAcceptable documentation for a change in local flow conditions to support the adjustment of the\nanalysis duration includes:\n A current and old FIS showing the before and after changes in local flow conditions\n A Conditional Letter of Map Revision (CLOMR) or Letter of Map Revision (LOMR)\ndocumenting the change in flow conditions\n An H&H study that accounts for the change in local flow conditions\n A letter on community letterhead from an official knowledgeable about the changes in\nlocal flow conditions (e.g., city engineer, local floodplain manager); see Figure 2.4\n Aerial photographs of the project area before and after the change in the watershed (see\nFigure 2.5)\n Other photographs showing development in the vicinity of the mitigation area with\ndocumentation of when the development occurred\nFigure 2.4 is a sample letter documenting the change in local flow conditions for a fictitious\nflooding source. The sample letter explains the drainage project that caused the changes in local\nflow conditions, including the year of construction, as well as documentation of the water surface\nelevations at the project location before and after the flow change. The letter is provided by a\nlocal official familiar with the hydrology and hydraulics in the area.\n2-15"}, {"page_number": 25, "text": "Flood Hazard Mitigation\nFigure 2.4: <PERSON><PERSON> letter documenting a change in local flow conditions\nFigure 2.5 is an aerial photograph showing significant development in a rural area that could\nchange local flow conditions. In this case, the user would need to provide the approximate year\nthe development most significantly affected local flow conditions, as well as before- and after-\ndevelopment photographs.\n2-16"}, {"page_number": 26, "text": "Flood Hazard Mitigation\nFigure 2.5: Sample before- and after-development photographs documenting a change in land use\n(<PERSON><PERSON> 2001)\n2.1.3.2 Entering the Analysis Duration in the DFA Module\nThe following example illustrates using adjusted analysis duration along with historic flood\nclaims data to complete a BCA using the DFA module.\nExample: Residential Property at 1234 Lake Drive, Jacksonville, FL\nScope of Work\nThe goal of the mitigation project is to acquire the residential property at 1234 Lake Drive,\nJacksonville, FL, to remove the property from the Flood Creek Floodplain. The house, built in\n1970, is a repetitive loss parcel with four reported flood claims in 15 years. The total mitigation\nproject cost is $150,000. This project meets the State of Florida’s goal to reduce the number of\nrepetitive loss properties. Once the property is acquired, the City will demolish the house, and\nreturn the property to its original state within the floodplain. A permanent open space and\nconservation easement will be recorded with the title and deed to the property.\nData Provided\nThe following data are provided for the BCA DFA module:\n1. Scope of Work (SOW) and acceptable documentation to properly justify the appropriate\nvalues entered in the DFA module.\n2. Flood claims data from 1996, 1999, 2001, and 2004, as shown in the table below.\nExample of flood claims data from 1996, 1999, 2001 and 2004. The data includes the dates the\nfloods occurred, the dollar amounts paid for building damages claims, and the dollar amounts\npaid for building contents claims.\nDamage Year Date of Loss Building Damage Paid Building Contents Paid\n1996 6/13/1996 $20,000 $15,000\n1999 5/31/1999 $29,000 $15,000\n2001 7/4/2001 $24,000 $15,000\n2004 6/24/2004 $35,000 $15,000\n2-17"}, {"page_number": 27, "text": "Flood Hazard Mitigation\n3. BCA analysis year (2011) and year the structure was built.\n4. Project useful life of 100 years (FEMA standard value for acquisition).\n5. Integrated unknown frequency calculator to calculate the recurrence intervals.\nApproach\n1. Create a structure for the property at 1234 Lake Drive and associate the structure with a\nproject.\n2. Select Damage-Frequency Assessment on the Mitigation Information screen. Click Save and\nContinue.\n2-18"}, {"page_number": 28, "text": "Flood Hazard Mitigation\n3. <PERSON><PERSON> <PERSON> as the hazard and Acquisition as the mitigation type. Select Historical\nDamages as the basis for damages. Enter “4” for the number of damage events and “0” for\nthe number of events that have known recurrence intervals. Click Save and Continue.\n4. On the Cost Estimation Info screen, enter the Project Useful Life as 100 years and the\nMitigation Project Cost as $150,000. Click Save and Continue.\n2-19"}, {"page_number": 29, "text": "Flood Hazard Mitigation\n5. Using the information from Figure 2.4, enter “17” for the User Input Analysis Duration on\nthe Historic Damages-Before Mitigation screen to override the computed analysis duration.\n6. Click on the View Damages button to view the recurrence intervals determined by the\nunknown frequency calculator.\n2-20"}, {"page_number": 30, "text": "Flood Hazard Mitigation\n2.1.4 Using National Flood Insurance Program BureauNet Data\nBureauNet is a web-based database that contains information on all National Flood Insurance\nProgram (NFIP) policies and claims since 1978. The database enables users to retrieve data by\nsearching for specific flood insurance policy numbers, property addresses, or owners. These data\nare often useful for determining historic damages to specific buildings resulting from a specific\nflood event. The damage values obtained from BureauNet can be used in the DFA module as\nhistorical damages.\n2.1.4.1 Access to BureauNet\nWhile access to BureauNet is limited to specific FEMA personnel and various individuals at the\nState level who are granted access based on a need-to-know basis, subapplicants may be able to\nrequest BureauNet data through the following contacts:\n Designated State Floodplain Manager. A current list of State Floodplain Managers is\navailable on the Association of State Floodplain Managers (ASFPM) Web site at:\nhttp://www.floods.org/index.asp?menuID=274&firstlevelmenuID=185&siteID=1. The\ndesignated State Floodplain Manager may have direct access to BureauNet.\n State Hazard Mitigation Officer. A current list of SHMOs is available on the FEMA Web\nsite at: http://www.fema.gov/about/contact/shmo.shtm. The SHMO may have direct\naccess to BureauNet or can submit a request to the appropriate FEMA Region to obtain\nBureauNet data.\nIf local or State contacts do not have access to BureauNet data, the FEMA Regional office can be\ncontacted. Users should initiate contact with one of the above-listed individuals well in advance\nof their deadline to ensure adequate time for personnel to retrieve the data. To facilitate the\nprocess, a list of property addresses, insurance policy numbers or owner name(s) at the time of\nthe flood event (if known), and flood event dates should be provided to the contact person.\n2.1.4.2 Flood Insurance Coverage\nFlood insurance protects two types of insurable property: building and contents. Building\ncoverage insures the building, and contents coverage insures possessions. Neither building nor\ncontents coverage insures the land.\nBuilding coverage includes:\n The insured building and its foundation\n The electrical and plumbing system\n Central air conditioning equipment, furnaces, and water heaters\n Above-ground appliances and other items of property that are considered part of the\nbuilding (i.e., refrigerators, stoves, and dishwashers)\n Permanently installed cupboards, bookcases, cabinets, paneling, and wallpaper\n Elevator equipment, fire sprinkler systems, light fixtures, plumbing fixtures\n Permanently installed carpeting or wood flooring over unfinished flooring\nContents coverage includes:\n2-21"}, {"page_number": 31, "text": "Flood Hazard Mitigation\n Clothing, furniture, and electronic equipment\n Curtains\n Portable and window air conditioners\n Portable microwaves and dishwashers\n Carpeting that is not already included in property coverage\n Clothing washers and dryers\nFlood insurance is available to homeowners, renters, condominium owners/renters, and\ncommercial owners/renters. All policy forms include coverage for buildings and contents; the\nbuilding coverage is required, while contents coverage is optional. Therefore, if contents\ncoverage was not purchased by the policy owner, flood insurance claims may list claim\npayments only for building claims.\n2.1.4.3 Estimated Flood Damages versus Actual Claims Payments\nFollowing a damaging flood event, the owner begins the claims process by identifying the\ndamaged versus the undamaged property and works with the adjuster to prepare a damage\nestimate. A payment is issued once proof of loss is provided and the insurance claim is\nsubstantiated. It is important to note that estimated flood damages may differ from the actual\nclaims payment. In many cases, the actual claims payment may be lower than the estimated flood\ndamages because certain items were not covered under building or contents coverage. As\ndescribed in the following subsections, the actual claims payments should be used for inputting\ninto the DFA module, not the estimated flood damages.\n2.1.4.4 Interpreting BureauNet Claims Data\nFigure 2.6 shows a sample flood claim for a fictitious single-family residence that sustained\ndamage during a 1974 flood. The relevant items to use in the DFA module are annotated and\ndiscussed below.\n2-22"}, {"page_number": 32, "text": "Flood Hazard Mitigation\nFigure 2.6: Sample NFIP flood claim\n2-23"}, {"page_number": 33, "text": "Flood Hazard Mitigation\nNote Explanation\nThe Date as of field indicates the date that the flood claim was retrieved from the\nBureauNet database.\nThe Policy Number field indicates the insurance policy number under which the\ncurrent policy was written.\nThe Dt of Loss field indicates the date when the flood damage occurred. This date\nshould be consistent with the damage year for the events the user includes in the DFA\nmodule.\nThis section shows the insured’s name (owner) and the property address.\nThis section shows information relevant to building coverage and claims payments.\n RCV stands for Replacement Cost Value and is the cost to replace the damaged property.\n ACV stands for Actual Cash Value and is the RCV at the time of loss minus physical\ndepreciation.\n Bldg Val is the value of the building, both in terms of RCV and ACV.\n Bldg Dmg is the estimated cost of building damages on the date of loss, both in terms of\nRCV and ACV.\n Bldg Paid is the actual claim amount paid out to the owner. If an amount is specified\nunder Bldg Final Pay, this is the final building claim amount paid out to the owner due to\nvarious adjustments.\n For purposes of obtaining historical damages for the DFA module, it is appropriate to\nselect the maximum value among the Bldg Dmg-ACV, Bldg Dmg-RCV, Bldg Paid, and\nBldg Final Pay.\nThis section shows information relevant to building content coverage and claims\npayments.\n RCV and ACV are defined similarly as in Note #5.\n Cont Dmg is the estimated cost of building contents damages on the date of loss, both in\nterms of RCV and ACV.\n Cont Paid is the actual claim amount paid out to the owner. If an amount is specified\nunder Cont Final Pay, this is the final contents claim amount paid out to the owner due to\nvarious adjustments.\nFor purposes of obtaining damages for the DFA module, it is appropriate to select the\nmaximum value among Cont Dmg-ACV, Cont Dmg-RCV, Cont Paid, and Cont Final\nPay.\n2-24"}, {"page_number": 34, "text": "Flood Hazard Mitigation\n2.1.4.5 How to Use NFIP BureauNet Data with the DFA Module\nBureauNet flood claims data can be used to complete a BCA using the DFA module with\nhistorical damages. The following example of an acquisition project illustrates this concept by\nproviding step-by-step directions for:\n Entering building and contents loss data from BureauNet for several events into the DFA\nmodule\n Using the integrated unknown frequency calculator to assign recurrence intervals for the\nevents\n Completing the DFA module to determine whether the acquisition project is cost\neffective (BCR is greater than or equal to 1.0)\n Estimating additional losses in the form of displacement costs using DDFs from the\nFlood module\n Entering the displacement costs back into the DFA module to complete the analysis.\nThe first step is to obtain BureauNet NFIP Flood Claims data. For this example, losses were\nobtained for the years 1974, 1985, 2001, and 2004, as shown in Table 1.\nTable 1: Example of BureauNet NFIP Claims Data\nBuilding Building\nDamage Year Date of Loss Damage Paid Contents Paid\n1974 6/13/1974 $16,750.00 $15,000.00\n1985 5/31/1985 $29,490.00 $15,000.00\n2001 7/4/2001 $23,750.00 $10,000.00\n2004 6/24/2004 $34,765.00 $17,000.00\n2.1.4.6 Enter NFIP Data into DFA Module and Determine BCR\n1. Once BureauNet data has been organized into the above table, create a project and a structure\nfor the property and associate the structure with the project. Select Damage-Frequency\nAssessment on the Mitigation Information screen. Click Save and Continue.\n2-25"}, {"page_number": 35, "text": "Flood Hazard Mitigation\n2. On the Hazard and Mitigation Info screen, choose flood as the Hazard and acquisition as the\nMitigation Type. Select Historical Damages as the basis for damages. Enter “4” for the\nnumber of damage events and “0” for the number of events with known recurrence intervals.\nClick Save and Continue.\n2-26"}, {"page_number": 36, "text": "Flood Hazard Mitigation\n3. On the Cost Estimation Info screen enter the Project Useful Life as 100 years (for\nacquisition) and enter the Mitigation Project Cost. For this example a mitigation project cost\nof $139,500 is used. Click Save and Continue.\n4. On the Type of Services screen, select Not Applicable. Click Save and Continue.\n5. On the Historic Damages Before Mitigation screen, enter the year built (in this example,\n1960). Add a column for “Building $” damages in the Historic Damages Before Mitigation\ntable. Use the BureauNet flood claims data to enter the damage years and building damages\n2-27"}, {"page_number": 37, "text": "Flood Hazard Mitigation\nunder the Building $ column. Allow the tool to inflate these values by selecting “No” to the\nquestion “Are damages in current day dollars?” for each damage year. Make note of the Total\nInflated building damage values for use in subsequent calculations in a separate table. Click\nSave and Continue.\n6. Add a column for “Contents $” damages. Use the BureauNet flood claims data to enter the\nbuilding contents damages for each damage year into this column. The tool will use the\nintegrated unknown frequency calculator to compute the recurrence intervals. The calculated\nrecurrence intervals can be viewed by clicking on the View Damages button at the bottom of\nthe Historic Damages Before Mitigation table. Make note of the recurrence intervals.\n7. Close the Expected Annual Damages screen and click Save and Continue.\n2-28"}, {"page_number": 38, "text": "Flood Hazard Mitigation\n8. In the Damages After Mitigation screen, it is not necessary to input any information since the\nproject involves an acquisition and there are no residual damages because the building will\nno longer exist. Click Save and Continue.\n9. The structure BCR can be viewed on the Summary of Benefits screen. For this example, the\nBCR is 0.74, indicating that this project is not cost effective when considering only building\nand contents damages. Section 2.1.4.7 describes how to maximize benefits in the DFA\nmodule.\n2-29"}, {"page_number": 39, "text": "Flood Hazard Mitigation\n2.1.4.7 Using the BCA Full Data Flood Module to Maximize Benefits in the DFA Module\nBenefits in DFA module can be maximized by incorporating displacement costs obtained from\nDDFs in the BCA Full Data Flood module. In the example described in Section 2.1.4.5, inputting\ndamages for building and building contents resulted in a BCR of 0.74, which is less than 1.0. The\nfollowing example builds off the BCA completed in Section 2.1.4.6 and illustrates how benefits\ncan be maximized to bring the BCR to above 1.0.\n1. Compile the data from the DFA module (Table 2). The table should include data from the\nDFA module including the recurrence intervals determined from the unknown frequency\ncalculator (Step 6 of Section 2.1.4.6) and the inflated building damages (Step 5 of Section\n2.1.4.6). It should also include the total building replacement value (BRV); for this example,\nuse $105,600. Add a column for the percent building damage. Divide the inflated building\ndamages by the total BRV for each event to determine the percent building damage. This\ndata will be used to find displacement costs associated with damages from the flood claims.\nTable 2: Example Data Obtained from DFA Module\nRIs from Unknown Inflated Building Percent Building\nTotal BRV\nFrequency Calculator Damages from DFA Damage\n13 $33,015 $105,600 31.3%\n17.3 $43,023 $105,600 40.7%\n26 $61,897 $105,600 58.6%\n85.3 $73,012 $105,600 69.1%\n2. Return to the BCA Software Project screen and enter Flood as a new mitigation type on the\nMitigation Information screen. Double-click on the Flood row to begin the Flood module.\n3. Select Acquisition as the flood mitigation project type. Click Save and Continue.\n2-30"}, {"page_number": 40, "text": "Flood Hazard Mitigation\n4. On the Full Flood-Questionnaire screen indicate that an FIS is available. Answer “Yes” to all\nof the follow-up questions on this screen.\nNote that the only information needed from this module is the DDFs and therefore other\ninformation is not important. The Flood module will not actually be used to evaluate the cost-\neffectiveness of the project. Click Save and Continue.\n5. Skip input on the Flood Data Source screen, since the data are for information only and do\nnot affect calculation of the BCR or the DDFs. Click Save and Continue.\n6. Enter data on the Structure Information screen. This data affects which DDF is selected.\nEnter the total size of the building, BRV, and residential structure details. For this example\nthe residential building has two stories, no basement, a building size of 1,600 square feet (sf),\nand a BRV of $66 per sf. The Demolition damage threshold should be entered as “50%” for a\nresidential building. Click Save and Continue.\n2-31"}, {"page_number": 41, "text": "Flood Hazard Mitigation\n7. On the Residential Structure Information screen select “USACE Generic” from the DDF\ndrop-down menu. The values for the DDFs will auto-populate. Select the default button for\nDisplacement Costs and Building Contents.\n8. On the Depth Damage Functions screen read the results on the Building tab. The USACE\ngeneric DDF for residential buildings shows building damages based on flood depth. A\nbuilding DDF is an estimate of damage that will occur to a building based on the depth of\nflooding and as a percentage of the BRV. For this example, the DDF calculates the estimated\nbuilding damages associated with 1 foot of flooding as $16,051 (or 15.2 percent of the BRV).\nAt 9 feet of flooding the structure would be more than 50 percent damaged; buildings that are\n2-32"}, {"page_number": 42, "text": "Flood Hazard Mitigation\n50 percent damaged are considered a total loss, and the building damage would be equal to\nthe BRV.\n9. Read the results on the Displacement tab of the Depth Damage Functions screen. The curve\nfor the USACE Generic DDF shows displacement time in days and cost in relation to the\nflood depth. In this example, displacement for a 1-foot flood depth is 45 days and over a year\nfor a 9-foot flood depth.\n2-33"}, {"page_number": 43, "text": "Flood Hazard Mitigation\n10. Using the percent building damage value calculated in Table 2 (see Step 1), find the closest\npercentage listed in the DDF under the Building tab for each of the known recurrence\nintervals. In this example, use the 13-year flooding event on Table 2. Per Table 2, the percent\nbuilding damage for the 13-year flooding event is 31.3%. Compare this to the DDF Building\ntab data. The closest DDF building damage value is 31.4%, which corresponds to a 4.0-foot\nflood depth. Repeat this step to determine the flood depth for each of the percent building\ndamage values shown in Table 2 (interpolate for damage percentages between the listed\nvalues).\n11. Use the DDF flood depths to find the before-mitigation displacement cost by selecting the\nDisplacement tab on the Depth Damage Functions screen. A 4.0-foot flood depth results in\n180 displacement days, which corresponds to a displacement value of $13,635. Keep track of\nthe displacement values by updating Table 2 (refer to Table 3).\n2-34"}, {"page_number": 44, "text": "Flood Hazard Mitigation\nTable 3: Example of Approximate Displacement Cost Determination\nFrom Table 2 (Step 1) From Steps 2 - 11\nRIs from Inflated Closest\nPercent Associated Approximate\nUnknown Building Total Percentage\nBuilding Flood Displacement\nFrequency Damages BRV Listed in\nDamage Depth (ft) Cost\nCalculator from DFA the DDF\n13 $33,015 $105,600 31.3% 31.4% 4.0 $13,635\n17.3 $43,023 $105,600 40.7% 40.7% 6.0 $20,452\n26 $61,897 $105,600 58.6% 58.7% 11.0 $37,495\n85.3 $73,012 $105,600 69.1% 69.2% 16.0 $54,539\n12. Return to the Project Structures Summary screen and create a new structure within the\nproject to enter the approximate displacement costs corresponding to the four recurrence\nintervals. The software will add the benefits from the two structures to determine an\naggregated BCR.\nThe user should understand that this new structure does not represent an actual physical\nbuilding. It must be created because two different analysis methods were used to evaluate\nlosses. The structure containing building and contents losses was analyzed using historical\ndamages and “0” for the number of events that had known recurrence intervals. The software\nmanually inflated the building and contents losses to present-day values. For the structure\ncontaining displacement costs, the values will be entered as expected damages with known\nrecurrence intervals. The values are already present-day values and if they were entered\nsimply as a new column in the first structure, they would have been inflated incorrectly.\n2-35"}, {"page_number": 45, "text": "Flood Hazard Mitigation\n13. Within the new structure, select DFA on the Mitigation Information screen.\n14. On the Hazard and Mitigation Information screen, the hazard and mitigation type remain\nunchanged. The user should select Expected Damages as the basis for the damages and enter\n“4” for the estimated damage events and “4” for the events with known recurrence intervals.\nClick Save and Continue.\n15. On the Cost Estimation Info screen, enter the Project Useful Life (years) as 100 years. Since\nthe software will aggregate both costs and benefits from the two structures, enter the\nMitigation Project Cost as “$0” to avoid double-counting project costs. The mitigation\nproject cost was already entered in the first structure using the DFA module. Click Save and\nContinue.\n2-36"}, {"page_number": 46, "text": "Flood Hazard Mitigation\n16. On the Type of Services screen, select Not Applicable. Click Save and Continue.\n17. On the Expected Damages Before Mitigation screen, enter the four recurrence intervals\nshown in Table 3 (Step 11). Enter the Year Built. Add a column for “approximate\ndisplacement costs” under Expected Damages Before Mitigation, and enter the displacement\ncost values from Table 3. Click Save and Continue.\n18. Since this example is an acquisition project, there are no expected displacement costs after\nmitigation because the building will no longer exist. Click Save and Continue.\n2-37"}, {"page_number": 47, "text": "Flood Hazard Mitigation\n19. The additional displacement cost benefits can be viewed as Benefits Minus Costs on the\nSummary of Benefits screen.\n20. Review the Project Inventory screen for the aggregated benefits from both structures that\nmake up the project and the resulting project BCR. Note that inclusion of the displacement\ncosts calculated through use of the Flood module result in an increase of the project BCR\nfrom 0.74 (refer to Step 9 of Section 2.1.4.6) to 1.01.\n2.2 Working with the Flood Module\nThe Flood module analyzes proposed mitigation projects based on flood hazard conditions of\nriverine and coastal flood sources before and after implementing mitigation. The Flood module is\ndesigned for evaluating individual buildings within a project. The Flood module is recommended\nfor BCAs when users have detailed flood hazard information and structural data. The following\ndescribes the essential flood hazard and structural data required to use the Flood module.\nFlood Hazard Information\n FIRM with cross-sections and a flood profile\n H&H study (if the project area is unmapped or outside the SFHA)\n Streambed elevation (riverine flood hazard analysis only)\n Stillwater elevations (coastal flood hazard analysis only)\n BFE\n Flood elevations for the 10-, 50-, 100-, and 500-year recurrence intervals\n Flood discharge rates for the 10-, 50-, 100- and 500-year recurrence intervals (riverine\nflood hazard analysis only)\n2-38"}, {"page_number": 48, "text": "Flood Hazard Mitigation\nStructural Information\n FFE for a building affected by riverine flooding or located in a Coastal A Zone\n FFEs of the lowest floor member for buildings located in coastal Zone V\n Building type and size of building\n BRV\n Foundation type\nOver the past several National Technical Review periods, hundreds of flood mitigation grant\nsubapplications have been submitted for the HMA funding and about 40 to 50 percent failed to\ndemonstrate cost effectiveness. Common errors made in the BCA using the Flood module\ninclude selecting an incorrect building type and size when there is a partially finished basement,\nusing incorrect FFEs, and over-estimating the loss of public services. This section includes\ninformation on how counting damages for finished and unfinished basements, using topographic\nmaps with 2-foot contour intervals to estimate FFEs, incorporating loss of services for critical\npublic facilities, and how to calculate other losses and non-traditional benefits.\n2.2.1 Counting Damages for Finished or Unfinished Basement\nFor the purposes of conducting a BCA and applying the various DDFs available in the Flood\nmodule, the basement is considered finished if there are documented features similar to the main\n(first) floor, including finished flooring (e.g., carpeting, tile, vinyl, wood), finished walls (e.g.,\nframing, insulation, drywall, paneling), finished ceilings, finished electrical features (e.g.,\noutlets, lighting), fully heated/air conditioned, and typical household contents.\nAcceptable documentation for a finished basement includes: photographs, tax assessments,\nbuilding permits, as-built construction plans, copy of a current insurance policy, and/or detailed\nreal estate appraisals. If the basement is fully finished, the elevation of the basement floor may\nbe used as the FFE in the BCA Flood module; in this case, the DDFs in the Flood module must\nbe adjusted to reflect the elevation that flood water can enter the building.\n2.2.1.1 Unfinished Basements\nFor a building with an unfinished basement, the FFE is for the first habitable floor, even if the\ncellar, crawlspace, or basement has window wells or door(s) (see Figure 2.7). The building type\nselected for the Residential Structure Details on the BCA Structure Information screen will be\neither one-story with a basement or two-story with a basement. The building, contents, and\ndisplacement DDFs are used without adjustment. In addition, the term “first habitable floor”\nrefers to the first habitable floor above the basement, regardless if the basement is finished or\nunfinished.\n2-39"}, {"page_number": 49, "text": "Flood Hazard Mitigation\nFigure 2.7: FFE of an unfinished basement\nThe following example illustrates how to calculate damages for an unfinished basement using\nthe Flood module. Assume a one-story house with an unfinished basement. The basement floor\nelevation = 980.00 feet National Geodetic Vertical Datum (NGVD) and the first habitable floor =\n988.00 feet NGVD.\n1. Enter 988.00 feet NGVD as the FFE.\n2. Enter the square footage. The square footage reported for the structure size should be equal to\nthe square footage of the first habitable floor.\n3. Select the DDF. The DDF used by the Flood module is based on answers to the software\nquestionnaire. If the user selects a DDF other than the default or a DDF from the Library,\nadequate documentation must be provided in the subapplication materials to support the user-\nentered adjustments.\n2.2.1.2 Finished Basement with Window Elevation Above or Below the Adjacent Ground\nElevation\nFor buildings with a finished basement with the bottom of the window elevations either above or\nbelow the adjacent ground elevation, the FFE is the top of the basement floor elevation (Figure\n2.8).\n2-40"}, {"page_number": 50, "text": "Flood Hazard Mitigation\nFigure 2.8: FFE and DDF offset for finished basement\nThe finished basement is considered the first floor. For buildings with finished basements, if the\nDDFs are not set to zero below the entry point for flood water, the Flood module will greatly\noverestimate damages and the BCR because it will calculate damages for several feet of flood\nwater at elevations that cannot enter the building. Users should perform the following actions to\nprevent this error.\n1. Users should select two or more stories without basement as the building type under\nResidential Structure Details on the BCA Structure Information screen.\n2. Set the building, contents, and displacement DDFs to zero below the lowest opening. This\ncan be performed in two ways:\na) The recommended method is as follows: select the Library button on the Residential\nStructure Information screen under Depth Damage Function Type. Select the DDF,\nand then enter “0” for flood depths below the entry point in the Before Mitigation\nUser Entered (Pct) DDF column for the Building, Contents and Displacement tabs\n(recommended method for whole foot offsets)\nb) An alternative is as follows: select the Custom button on the Residential Structure\nInformation screen under Depth Damage Function Type. Enter the default DDF\nvalues in the Before Mitigation User Entered DDF column starting at the flood depth\nthat flood water enters the building. DDF values for non-integer flood depths must be\ninterpolated.\nAfter adjusting the DDF, calculation of damages will begin at the elevation of either the lowest\nwindow opening (for basement windows above grade) or at ground elevation adjacent to the top\nof a below-grade window (i.e., top of the window well).\n2-41"}, {"page_number": 51, "text": "Flood Hazard Mitigation\nThe following example illustrates how to calculate damages for a finished basement using the\nFlood module. Using Figure 2.8 as an illustration of a one-story house with a finished basement,\nassume the basement floor elevation = 980.00 feet NGVD, the first habitable floor elevation =\n988.00 feet NGVD, and the bottom of the basement window = 985.00 feet NGVD. Assume that\nthe window is not in a window well and the ground adjacent to the window is below the bottom\nof the window.\n1. Set the building type as two or more stories without a basement.\n2. Set the FFE as 980.00 feet NGVD. The basement floor elevation is used as the FFE.\n3. Enter the square footage. The square footage should equal the square footage of the first\nhabitable floor plus the square footage of the basement. Note the unit cost BRV (in $/sf) may\nneed to be adjusted when the area of the finished basement is included so that the combined\nBRV for the basement and the first habitable floor is not greater than the total BRV for a one-\nstory house with a finished basement (i.e., the BRV using this method cannot exceed the total\nactual BRV).\n4. Adjust the DDFs for building, contents, and displacement so there are zero damages from\nelevation 980.00 through 984.00 feet NGVD. Table 4 (below) shows the adjustments that\nwould be made for building damage in this example. Similar DDF adjustment would be\nmade for contents (in percent) and displacement (in days). Notice the DDFs show damages\nstarting at the +5.0-foot mark (i.e., the offset), or at elevation 985.00 feet NGVD for all three\ncategories.\n2-42"}, {"page_number": 52, "text": "Flood Hazard Mitigation\nTable 4: Example for Residence with Two or More Stories without Basement\nBefore Mitigation Before Mitigation ($) Before Mitigation ($) Before Mitigation ($)\nFlood Depth (ft)\nUser Entered (Pct) BUILDING CONTENTS DISPLACEMENT\n-2.0 0.0%\n-1.0 0.0%\n0.0 0.0%\n1.0 0.0%\n2.0 0.0%\n3.0 0.0%\n4.0 0.0%\n5.0 $61,902 $36,423 $19,174\n6.0 $69,597 $40,869 $23,008\n7.0 $76,779 $44,973 $26,843\n8.0 $83,448 $48,564 $30,678\n9.0 $171,000 $51,813 $34,513\n10.0 $171,000 $54,720 $38,347\n11.0 $171,000 $57,114 $42,182\n*Note: Before-mitigation percentages are different for building and contents and are represented as days of displacement.\nThese values are not included in Table 4. Dollar values obtained are based on structural information and are only provided as\nan illustration-—dollar values will vary for each specific structure.\n******* Finished Basement with Walkout or a Garage Elevation Equal to the Adjacent Ground\nFor a building with a finished basement that has a walkout or a garage equal to the elevation of\nthe adjacent grade, the FFE is equal to the walkout or garage floor elevation (Figure 2.9).\nFigure 2.9: FFE for a finished walkout basement\n2-43"}, {"page_number": 53, "text": "Flood Hazard Mitigation\nThe following example illustrates how to calculate damages for a building with a finished\nbasement using the Flood module. Using Figure 2.9 as an illustration of a one-story house with a\nfinished basement, assume the basement floor elevation = 980.00 feet NGVD, the walkout or\ngarage elevation = 980.00 feet NGVD, and the adjacent ground elevation = 980.00 feet NGVD.\n1. On the Residential Structure Details tab of the Structure Information screen, select Two or\nMore Stories and click “No” to indicate the lack of a basement.\n2. Do not adjust the DDFs; these are used without adjustment by the user.\n3. Enter the square footage of the first habitable floor plus the square footage of the basement.\nNote the unit cost BRV (in $/sf) may need to be adjusted when the area of the finished\nbasement is included so that the combined BRV for the basement and the first habitable floor\nis not greater than the total BRV for a one-story house with a finished basement (i.e., the\nBRV using this method cannot exceed the total actual BRV).\n2.2.1.4 Partially Finished Basement\nThe recommended approach for partially finished basements is to conduct two BCA runs by\ndividing the house into two buildings. For a building with a partially finished basement (i.e., at\nleast part of the basement is fully finished), the first FFE is equal to the top of the basement floor\nelevation with the appropriate adjustment described in Sections 2.2.1.2 or *******, and the second\nFFE is equal to the first habitable floor. One building is a “two or more stories without a\nbasement” and the other is either a “one- or two-story with a basement.”\nThe following example illustrates how to calculate damages for a building with a partially\nfinished basement using the Flood module. Assume a 1,000 sf house is one story with a full\n(1,000 sf) basement, where 60 percent (600 sf) of the basement is fully finished and 40 percent\n(400 sf) is unfinished. Assume a BRV of $120/sf for a total BRV of $120,000 (this includes\nadjustments for both the finished and unfinished basement).\n1. Create two structures within a project for the Flood module. Begin with the BCA module run\nfor the first building, which is the part of the house with the finished basement and the first\nfloor area directly above it (Building Type A in Figure 2.10).\n2-44"}, {"page_number": 54, "text": "Flood Hazard Mitigation\nFigure 2.10: Partial finished basement, divided into two structures\na) On the Structure Information screen, select the Two or More Stories for the\nresidential building type and click “No” to indicate the lack of a basement.\nb) Enter data for the Total size of building (sf). The area is the 60 percent of the first\nfloor area plus the finished basement area: (600 sf) + (600 sf) = 1,200 sf.\nc) Enter a $60/sf BRV for the total finished areas. This sets the BRV for building A to\n60 percent ($72,000) of the total BRV ($120,000 for Building Types A and B).\nd) Enter the FFE as the top of the finished basement floor.\ne) Adjust the DDFs (described in Section 2.2.1.2) to account for the elevation that flood\nwater can enter the house, if applicable.\n2-45"}, {"page_number": 55, "text": "Flood Hazard Mitigation\n2. The second BCA module run will be for the second building representing the unfinished\nbasement and the first floor area directly above it (Building Type B in Figure 2.10).\na) On the Structure Information screen, select building type as One Story and click\n“Yes” to indicate the presence of a basement.\nb) Enter data for the Total size of building (sf). The area is 40 percent of the first floor\narea: (400 sf).\nc) Enter the FFE as the top of the first habitable floor (in this case, the above-grade\nfloor).\nd) Enter the adjusted BRV of $120/sf. This sets the BRV for building B to 40 percent\n($48,000) of the total BRV of $120,000. Note that the sum of the BRV of Building\nType A and Building Type B cannot exceed the total actual BRV.\ne) The DDFs should not be adjusted.\nf) Enter the project cost. Do not enter the full project cost for both structures because\nthat would double-count the project cost. Enter the total project cost for one structure\nand zero cost for the other.\nDiscussion\nFor situations with vehicle damage or other contents damage (such as home improvement\nequipment or agricultural equipment) due to flooding of a garage, refer to Section 2.2.3.\nOccasionally flood damage can occur in a finished basement and the homeowner elects to leave\nthe basement unrecovered from recent flooding events. Subsequent floods may still affect the\n2-46"}, {"page_number": 56, "text": "Flood Hazard Mitigation\nbasement, but a lower percentage of building, contents, and displacement damages will occur. In\nanalyzing the initial flood damage to the finished basement, documentation must be provided\nthat indicates the basement was finished prior to the initial flooding event. Documentation\nincludes tax assessments, building permits, as-built construction plans, contractor receipts for\nfinishing the basement and/ or estimates for repairing the basement, or a copy of a previous\ninsurance policy or real estate appraisals, as well as photographs of the previously finished\nbasement and the current basement condition. It is unacceptable to claim the basement was\npreviously finished without acceptable documentation. All flood damage events subsequent to\nthe initial event that changed the basement from finished to unfinished should be analyzed as if\nthe basement is unfinished.\n2.2.2 Using Topographic Maps with 2-Foot Contour Intervals to Estimate First Floor\nElevations\nThe FFE of a building is required for using the Flood module, but is often difficult for users to\nobtain since a FEMA elevation certificate or an FFE from a licensed surveyor may be expensive.\nThis section provides an acceptable alternate method for estimating the FFE using detailed\ntopographic maps. In order to use this approach, the topographic map must be current and have a\nmaximum contour interval of 2 feet. Using a current topographic map ensures that the most\nrecent topography for the project area is used to determine the FFE. Especially in steep locations,\nthe elevations shown on an outdated map may differ significantly from a current map if the plot\nwas graded to construct a building foundation. A maximum 2-foot contour interval ensures that\nthe data are detailed enough such that it does not introduce a significant amount of error when\nestimating the FFE.\nWhile 2-foot contour interval maps are less common than larger interval maps, possible sources\ninclude USGS mapping, State Geological Survey mapping, engineering drawings, light detection\nand ranging (LiDAR) data, or City and County geographic information system (GIS)\ndepartments.\n2.2.2.1 Topographic Map Features\nA topographic map provides approximate ground elevations typically in the form of contour\nlines. The following are key features of every topographic map:\nTitle: The title of the map should indicate that the map is applicable to the proposed\nproject area.\nGrid: The grid system of a map, usually in the form of latitude/longitude coordinates or\nTownship/Range information, can be used along with the north arrow and features\nidentified on the legend to find the approximate building location and orientation. If the\nlatitude and longitude coordinates of the building are unknown, several online resources\ncan be used to estimate the coordinates based on the building address.\nLegend: The legend identifies key features on the map, which may include property\nboundaries, water sources, roads, etc.\nDatum: The vertical datum (e.g., NGVD and North American Vertical Datum [NAVD])\nrepresents the reference elevation used to create the map. When comparing water surface\nelevations and FFEs, care must be taken to identify the datum in case the elevations from\n2-47"}, {"page_number": 57, "text": "Flood Hazard Mitigation\none datum need to be converted to another. Online resources are available to convert\nelevations if the latitude and longitude coordinates are known.\nFigure 2.11 shows an example of a topographic map available from the Lake County, IL GIS\nDepartment (Lake County 2011) with the above features identified.\nMap\nGrid\nDatum\nMap\nLegend\nMap Title\nMap Scale North Arrow\nFigure 2.11: Example of topographic map (Lake County 2011)\nThe map scale provides the relationship between horizontal distance on the map and the actual\ndistance on the ground. Figure 2.12 shows two ways that the scale could be represented. The first\nis a ratio scale. The ratio scale on this map is 1:24,000. This indicates that 1 inch on the map\nrepresents 24,000 inches (0.38 miles) on the ground. Below the ratio scale is a graphic scale\nrepresenting distance in miles, feet, and kilometers. The space between the 0 and the 1 mile mark\non the scale is the distance a user must measure on the map to identify a 1 mile distance.\n2-48"}, {"page_number": 58, "text": "Flood Hazard Mitigation\nFigure 2.12: Example of a map scale representations\n2.2.2.2 Contour Interval\nContour lines connect a series of points of equal\nelevation and are used to illustrate relief on a map.\nContour lines that are close together represent steeper\nterrain, whereas contour lines that are far apart\nrepresent flatter terrain. A contour interval is the\nelevation change between two consecutive contour\nlines. For example, the topographic map in Figure 2.13\nshows a contour line labeled as 700 feet, followed by\nfour unlabeled lines, and a fifth line labeled as 800 feet.\nThis indicates that the elevation change between each\ncontour line, or the contour interval, is 20 feet. Note\nFigure 2.13: Example of contour lines\nthat this map cannot be used to determine the FFE\nand contour interval\nbecause the maximum contour interval is greater than 2\nfeet.\n2.2.2.3 Estimating the First Floor Elevation\nStep 1: Determine Building Location\nFigure 2.14 shows an example topographic map that shows two buildings. Using a combination\nof map features such as roads and aerial imagery, the two buildings were determined to lie in the\nsoutheast portion of the map.\n2-49"}, {"page_number": 59, "text": "Flood Hazard Mitigation\nBuilding 1\nBuilding 2\nFigure 2.14: Determining building location on a topographic map\nStep 2: Outline Building Footprint and Specify Location Used For Estimating the FFE\nIn Figure 2.15, the livable area of both buildings is outlined in green and the exact location on\nthe building used to estimate the FFE is indicated with arrows. Indicating the exact location is\nimportant for Step 3. When possible, an easily identifiable feature (e.g., a corner, entryway, etc.)\nlocated directly on a contour line should be chosen as it will avoid the extra step of interpolating\nthe ground elevation between contour lines, and may increase the accuracy of the estimate.\n2-50"}, {"page_number": 60, "text": "Flood Hazard Mitigation\nBuilding 1\nBuilding 2\nFigure 2.15: Outline the building footprint (shown in green)\nStep 3: Determine Ground Elevation at Indicated Location of Building\nFor Building 1, the ground elevation at the eastern corner (indicated with a red arrow) is\napproximately 846 feet since the corner lies on the 846-foot contour line. If the north corner were\nused instead, the ground elevation would need to be linearly interpolated. Since the north corner\nlies approximately halfway between the 844-foot and 846-foot contour lines, the ground\nelevation for that corner would be interpolated as 845 feet.\nFor Building 2, the corner indicated with a blue arrow lies directly on the 850- foot contour.\nTherefore, the ground elevation is estimated as 850 feet.\nStep 4: Estimate Height from Ground to Top of First Floor Slab\nThe difference in height between the ground and the top of the first floor must be measured at the\nindicated location in order to have the most accurate estimation of the FFE. This height may be\ndetermined by counting bricks, estimating the height from a photograph, or directly measuring\n2-51"}, {"page_number": 61, "text": "Flood Hazard Mitigation\nthe height in the field. Photographs must be provided as documentation with the subapplication\nregardless of the method used.\nFigure 2.16 shows that, although the first floor is itself level, the height used to determine the\nFFE from the ground may differ depending on the exact location used for estimating the ground\nelevation. In this figure, the left side of the building is located at the highest adjacent grade\n(HAG) elevation, while the right side is located at the lowest adjacent grade (LAG) elevation.\nFigure 2.16: Measure the height of the first floor slab\nAs shown in Figure 2.17, the height of the top of the first floor slab for Building 1 is 1.4 feet\nfrom the ground. For Building 2, the height to the top of the first floor slab is 1.5 feet from the\nground.\nFigure 2.17: Measure the height to the first floor slab\n2-52"}, {"page_number": 62, "text": "Flood Hazard Mitigation\nStep 5: Estimate FFE by Adding Height (Step 4) to Ground Elevation (Step 3)\nIn this example, for Building 1, the ground elevation (846 feet) plus the height to the first floor\nslab (1.4 feet) yields an FFE of 847.4 feet.\nFor Building 2, the ground elevation (850 feet) plus the height to the first floor slab (1.5 feet)\nyields an FFE of 851.5 feet.\n2.2.3 Other Losses and Non-Traditional Benefits\nThe most common benefit categories for mitigation projects include building damage, contents\ndamage, displacement costs, and loss of function impacts. For this Guidance, these benefit\ncategories will be termed “traditional.” There may be situations where a project is not cost-\neffective when only traditional benefits are considered and therefore, it may be advantageous to\ninclude other non-traditional benefits in the BCA. Non-traditional benefits may include the\navoidance of damage to landscaping equipment, agricultural equipment, outbuildings and\nvehicles. Non-traditional benefits can only be included for acquisition and flood control\nmitigation projects.\nThe following sections identify the types of non-traditional benefits that can be claimed in the\nFlood module and provide information on how to enter these benefits into the module.\n2.2.3.1 Damage Estimates for Other Losses and Non-Traditional Benefits\nThe following methods, listed in order of accuracy and preference, can be used to determine\ndamage estimates for other losses and non-traditional benefits:\n Method 1. Use historical damage data that affected a structure proposed for\nmitigation.\nFor example, a home proposed for acquisition may have documented historical records of\ndamage to a lawnmower and a wood shed that occurred at a flood depth of 2 feet (relative\nto the FFE of the house). Automotive damage may only be included when there is\ndocumented historical damage and only for acquisition projects. These damages can be\nentered in the BCA Flood module at a depth of 2 feet, as shown in Section *******. The\nsoftware will add these user-entered damages to the benefit calculations.\n Method 2. Use historical damage data from structures that are not part of a\nproposed mitigation project to estimate damages for structures proposed for\nmitigation.\nIn this case, the proposed project should have similar structures, equipment, risk, etc. to\nthose affected by the historical damages. For example, the home used in the previous\nexample could be used to estimate damages for a wood shed and a lawnmower at another\nhome provided that the wood shed is similar in materials and construction and the\nlawnmower is a similar type and stored in a similar location.\n Method 3. Use professional judgment to estimate damages that are not based on\nhistorical data.\nManufacturers of agricultural equipment can make credible estimates for equipment\ndamage at varying flood depths. It is important to note that this method cannot be used to\nestimate vehicle damages.\n2-53"}, {"page_number": 63, "text": "Flood Hazard Mitigation\nCredible decision making is critical to making the most accurate damage estimates possible,\nespecially for Methods 2 and 3. In addition, all data and assumptions must be supported by\nappropriate documentation. Considerations in making accurate damage estimates include, but are\nnot limited to the following:\n Are flood depths or recurrence intervals (RI) for historical data similar to flood depths\nand RIs used for a damage estimate? For example, historical outbuilding damages for a\n500-year flood should not be used as an estimate of damage for a 200-year flood.\n Is equipment for the historical data similar to the equipment used for the estimate? For\nexample, a gas lawnmower may have damage that differs from an electric lawnmower.\n Is the hazard condition for the historical damage similar to the hazard condition being\nmitigated? For example, a flash flood or flooding from a substandard flood wall may\ndiffer from riverine flooding in terms of flows or the ability to move equipment out of\nreach prior to being subject to flooding.\n Was a manufacturer, mechanic or supplier for equipment consulted for estimating flood\ndamage?\n Consider the mitigation project. Elevating a structure will not reduce damages to\nequipment located in garages and outbuildings; however, acquiring a property or\nreducing flood risk through flood control projects will.\n2.2.3.2 Acceptable Documentation for Non-Traditional Benefits\nAcceptable documentation for equipment, outbuildings, and vehicle damage may include:\n Insurance claims for landscaping and agricultural equipment damage\n Estimates of flood depths and damages based on a homeowner affidavit\n Photographs of damaged equipment, outbuildings and vehicles and the associated value\nof the damaged items\n FEMA Project Worksheets/Damage Survey Reports\n Records of historical vehicle damages, including repair invoices and insurance claims\n High water marks, which can be used to determine the flood depth at which damages\noccurred\n RIs based on FIS, H&H study, stream or tide gauge data, insurance records (if used to\nassess how often events occurred), and newspaper accounts citing credible sources such\nas a public agency\nFigures 2.18, 2.19, and 2.20 show examples of acceptable documentation to support estimates for\nnon-traditional benefits. The cost to repair or replace the item may be used in the BCA. Figure\n2.18 shows a sample insurance claim documenting landscaping equipment and outdoor property\ndamage. These damages should be associated with a flood depth or the RI of the flood event.\nFigure 2.19 shows a sample of repair records for vehicle damage. Only actual historical vehicle\ndamages may be included in the BCA (i.e., Method 1 and 2 only). Figure 2.20 shows a sample\nphotograph documenting vehicle damage due to flooding. Photographs may be used to estimate\nthe depth of flooding associated with the vehicle damage and can be used in conjunction with\ndocumentation such as shown in Figure 2.19 to estimate damages.\n2-54"}, {"page_number": 64, "text": "Flood Hazard Mitigation\nFlood Insurance Company Date of Loss: 10/10/2007\nClaim\nCustomer: <PERSON> Number: 1234C567\nPolicy\n100 Main St. Number: *********\nLouisville, KY\nCost to Settlement\nItem Qty Description Repair/Replace Depreciation Amount\n1 1 Chair $3200 $2600 $600\nLawn\n2 1 Mower $480 $390 $90\nWeed\n3 1 Whacker $65 $10 $55\nHedge\n4 1 Trimmer $75 $40 $35\n5 1 Skill Saw $125 $30 $95\nTotals $3,945 $3,070 $875\nFigure 2.18: Sample insurance claim documenting damage\nInvoice Date: 10/15/2007\nCar Repair Inc.\nLouisville, KY\nBill to: <PERSON>\n1 Main St.\nLouisville, KY\nQuantity Description Amount\n1 Flood damage repair to drivetrain, cleaning and body repair $2,500\nFigure 2.19: Sample of repair records for vehicle damage\n2-55"}, {"page_number": 65, "text": "Flood Hazard Mitigation\nFigure 2.20: <PERSON>ple photograph showing vehicle damage due to flooding\n******* Entering Non-Traditional Benefits Data in the BCA Flood Module\nThe following example illustrates how to enter non-traditional benefits in the Flood module.\nNon-traditional benefits can be entered in other BCA modules, but these applications are not\ndescribed in this document.\nExample: Acquisition of Residential Property in Louisville, KY\nScope of Work\nThe goal of the mitigation project is to acquire the residential property located at 100 Main\nStreet, Louisville, KY in order to remove the property from the Flood Creek floodplain. The\nhouse, built in 1970, is located in the FEMA-delineated flood Zone AE, and has an FFE of 13\nfeet NGVD. The adjacent streambed elevation is 10 feet NGVD. The home is one story on a slab\nfoundation without a basement. The total project cost is $150,000. The house is 1000 sf and has a\nBRV of $100/sf. In a 2007 flood event with an unknown RI, in addition to building, contents,\nand displacement damage, the property sustained damage to landscaping equipment and outdoor\nproperty. The City proposes to acquire and demolish the house, and convert the property to a\npermanent open space and conservation easement.\nData Provided\nThe following data are provided for the Flood module:\n1. SOW and acceptable documentation to properly justify the values entered in the Flood\nmodule\n2-56"}, {"page_number": 66, "text": "Flood Hazard Mitigation\n2. Water surface elevation, discharge data, and streambed elevation from the FIS as shown in\nTable 5.\nTable 5: WSEL and discharge data for 100 Main Street, Louisville, KY from FIS\nPercent\nRecurrence Annual Elevation Before Discharge Before Mitigation\nInterval (year) Chance (%) Mitigation (feet) (cfs)\n10 10% 14.5 120\n50 2% 16.2 250\n100 1% 17.1 360\n500 0.2% 18.2 440\n3. Insurance records for landscaping equipment, as shown in Figure 2.17 and repair records for\nvehicle damage, as shown in Figure 2.18 associated with flood depths, as shown in Table 6.\nTable 6: Other damages associated with flood depth\nDamage\nItem Amount Flood Depth (feet)\nEquipment $3,945 0\nVehicle $2,500 1.5\nThe associated flood depths were estimated from photographs and homeowner estimates. The\nequipment was assumed to be located at the building FFE. Based on the photographs (Figure\n2.20), the vehicle damages were estimated to occur at 1.5 feet of flooding.\n4. Project useful life of 100 years (FEMA standard value for acquisition)\nApproach\n1. Create a structure for the property at 100 Main Street and associate the structure with a\nproject.\n2. Select Flood on the Mitigation Information screen. Click Save and Continue.\n2-57"}, {"page_number": 67, "text": "Flood Hazard Mitigation\n2-58"}, {"page_number": 68, "text": "Flood Hazard Mitigation\n3. <PERSON>ose Acquisition as the mitigation type. Click Save and Continue.\n2-59"}, {"page_number": 69, "text": "Flood Hazard Mitigation\n4. Choose Flood Insurance Study (FIS) as the source of flood data and answer Yes to the\nquestions that appear after selecting FIS. Click Save and Continue.\n2-60"}, {"page_number": 70, "text": "Flood Hazard Mitigation\n5. On the Cost Estimation Info screen, enter the project useful life as 100 years and the\nmitigation project cost of $150,000. Click Save and Continue.\n2-61"}, {"page_number": 71, "text": "Flood Hazard Mitigation\n6. On the Flood Data Source screen, enter information for the Effective Date of FIS, FIRM\npanel number, FIRM effective date, and Community ID Number. Click Save and Continue.\nNote that this screen is informational only and will not affect BCR calculation.\n2-62"}, {"page_number": 72, "text": "Flood Hazard Mitigation\n7. On the Riverine Discharge and Elevation screen, enter data for the FFE, streambed elevation,\nand flood hazard data. Note that the red exclamation point next to the Enter the First Floor\nElevation box prompts the user to upload justification or supporting documentation for the\nFFE.\nThe FEMA Elevation certificate diagram description, Flood Source Name, and Flood Profile\nNumber are for informational purposes only and can remain blank; they will not affect the\nBCR calculation. Click Save and Continue.\n2-63"}, {"page_number": 73, "text": "Flood Hazard Mitigation\n8. On the Structure Information screen enter information for the building including square\nfootage, BRV, and structure details. Note that the red exclamation points prompt the user to\nupload justification or supporting documentation for the building size and BRV.\nThe Total value of building (BRV) will be calculated automatically by the software.\nDemolition damage threshold should be left at the default 50 percent. Click Save and\nContinue.\n2-64"}, {"page_number": 74, "text": "Flood Hazard Mitigation\n9. On the Residential Structure Information screen, select the applicable DDF. For this\nexample, select “default” under Depth Damage Function Type and “USACE Generic” under\nthe drop-down menu for Select Depth Damage Function. The Displacement Costs and\nBuilding Contents should be left as “default.” Click Save and Continue.\n2-65"}, {"page_number": 75, "text": "Flood Hazard Mitigation\n10. Click the button shown in screenshot to add a column on the Damages Before and After\nMitigation screen for both vehicle damage and equipment damage.\n2-66"}, {"page_number": 76, "text": "Flood Hazard Mitigation\n11. Enter the damage amounts at the closest flood depths from Table 6.\n12. Click Save and Continue twice to get to the final Summary of Benefits screen and the BCR.\n2-67"}, {"page_number": 77, "text": "Wind Hazard Mitigation\nSECTION THREE WIND HAZARD MITIGATION\nThere are two FEMA BCA modules that can be used to determine the BCRs for projects that\nprovide mitigation against high winds: the Hurricane Wind module and the Tornado Safe Room\nmodule.\nThe Hurricane Wind module is used to analyze the following building mitigation project types:\nwindow and door opening protection (shutters), load path structural retrofits, roof structural\nretrofits, and acquisitions. In the Hurricane Wind module, a key input is the wind hazard data\nwhich are included in the module. The default wind speed data (i.e., 3-second gust wind speeds\nfor multiple recurrence intervals) were obtained from the FEMA Hazards U.S. Multi-Hazard\n(Hazus) program. The wind speed data are automatically provided based on user input of the\nbuilding location, either by zip code or latitude and longitude coordinates. The user can override\nthe default wind speeds and enter wind speed data from other sources. This section describes\nhow to input wind speed data from the American Society of Civil Engineers (ASCE) into the\nHurricane Wind module.\nThe Tornado Safe Room module evaluates residential and community safe room mitigation\nprojects. The benefits in the module are based solely on providing life safety benefits for the safe\nroom occupants. Key input parameters include the maximum occupancy and usable area of the\nproposed safe room. This section defines these parameters and describes how to calculate them\nand enter the values into the Tornado Safe Room module.\n3.1 Deriving Wind Speed Data for the Hurricane Wind Module\nIn order to supply user-entered data for the BCA Hurricane Wind module, users first must derive\nwind speed data using ASCE 7-10 wind speeds (Minimum Design Loads for Buildings and Other\nStructures, ASCE/SEI 7-10). Wind speed data from ASCE 7-10 must be entered into a\nspreadsheet to create a graph. The graph allows users to extract wind speeds between recurrence\nintervals (i.e., derive wind speeds for a 35-year event when only the 25- and 50-year wind speeds\nare known). The graph is used to derive a formula, which calculates the needed wind speeds for\nthe BCA Hurricane Wind module. The methodology is applicable for all areas covered by the\nASCE 7-10 wind speed maps. When applying ASCE 7-10 wind speeds, it is important to note\nthat the mitigation project is assumed to be designed according to ASCE 7-10.\nApproach\n Determine the location of the site on a map showing county boundaries\n Identify wind speeds at the project location for the following seven recurrence intervals\nuse the following maps within ASCE 7-10:\n Figure CC-1, 10-year MRI 3 sec gust wind speed, pages 584 and 585\n Figure CC-2, 25-year MRI 3 sec gust wind speed, pages 586 and 587\n Figure CC-3, 50-year MRI 3 sec gust wind speed, pages 588 and 589\n Figure CC-4, 100-year MRI 3 sec gust wind speed, pages 590 and 591\n Figure 26.5-1C, 300-year MRI 3 sec gust wind speed, pages 249a and 249b\n3-1"}, {"page_number": 78, "text": "Wind Hazard Mitigation\n Figure 26.5-1A, 700-year MRI 3 sec gust wind speed, pages 247a and 247b\n Figure 26.5-1B, 1700-year MRI 3 sec gust wind speed, pages 248a and 248b\n Users are encouraged to verify the ASCE 7-10 wind speeds using the ATC Web site:\nhttp://atcouncil.org/windspeed/\n1. In a spreadsheet, enter the wind data obtained from ASCE 7-10 for each of the supplied\nrecurrence intervals.\n2. Use the Create Graph spreadsheet function to select a Scatter Graph\n3-2"}, {"page_number": 79, "text": "Wind Hazard Mitigation\n3. The software will insert a blank box. Right click the box and click Select Data\n4. Click on Select Data Source and then Add\n3-3"}, {"page_number": 80, "text": "Wind Hazard Mitigation\n5. Using the Edit Series box, select the recurrence intervals as the “Series X values” and the\nwind speeds as the “Series Y values.”\n6. <PERSON><PERSON> OK\n3-4"}, {"page_number": 81, "text": "Wind Hazard Mitigation\n7. Place the cursor over the X Axis, right click the mouse, and select Format Axis\n8. Under Axis Options select the “Logarithmic scale” and enter “10” for the base and click the\nClose button\n3-5"}, {"page_number": 82, "text": "Wind Hazard Mitigation\n9. Click on one of the data points and right click the mouse. Select Add Trendline\n10. Select Format Trendline. Under Trendline Options, select Logarithmic\n3-6"}, {"page_number": 83, "text": "Wind Hazard Mitigation\n11. Select Display Equation on chart and then click Close\n12. Type in the recurrence intervals used in the Hurricane Wind module (10, 20, 50, 100, 200,\n500, and 1,000 years) in any column within the spreadsheet you are working in, then type the\nwind speeds for the 10-, 50-, and 100-year events (the ASCE 7-10 and BCA software\nrecurrence intervals match for these three events).\n3-7"}, {"page_number": 84, "text": "Wind Hazard Mitigation\n13. Enter the wind speed equation shown on the developed graph. The X value will be the\nrecurrence interval. Round to the nearest wind speed.\n14. Copy the line equation to the 200-, 500-, and 1000-year events.\n3-8"}, {"page_number": 85, "text": "Wind Hazard Mitigation\n15. Enter the calculated wind speeds from the spreadsheet into the BCA Hurricane Wind module.\nThe user enters the data on the Wind Speed tab within the User Entered Wind Speed (mph)\ncolumn for each appropriate recurrence interval.\nBelow is a comparison of the Expected Maximum Wind Speed per Return Period. This can be\ndisplayed using the Show EANWS button near the center of the screenshot shown above. The\nscreenshot below (left side) shows the expected wind speeds as determined by the\nlatitude/longitude method using the Hurricane Wind module. The right side shows the expected\nwind speeds using the ASCE 7-10 wind speeds shown previously.\n3-9"}, {"page_number": 86, "text": "Wind Hazard Mitigation\n3.2 Working with the Tornado Safe Room Module\nThe Tornado Safe Room module is used to determine the BCR for construction of tornado safe\nrooms. The module requires the user to input the maximum occupancy, gross area, and usable\narea of the proposed safe room. These values are dependent on each other and are often\ncalculated incorrectly. This section describes identifying the target population, calculating the\ngross and usable area for a safe room, and calculating the maximum occupancy and provides\nsuggested sources of data and example calculations.\n3.2.1 Identifying the Target Population\nThe target population for a safe room is the population that can reach the safe room within 5\nminutes after notification of an approaching tornado. For tornado community safe rooms, the\ntravel limits are 5 minutes for walking and a maximum of 0.5 mile from the safe room for those\ndriving; therefore, the population of potential occupants must reside or work within 0.5 mile of\nthe safe room. Identifying the target population for a safe room includes delineating the area\nwithin a 0.5-mile radius of the safe room and then determining the population within that radius.\nTo delineate the target area, locate the proposed safe room on a map and draw a circle with a 0.5-\nmile radius (refer to Figure 3.1 for an example). The target population is determined from within\nthis 0.5-mile radius circle. The target population cannot be forced to cross a highway, railroad,\nriver, or any other obstacle that could put them in harm’s way. The use and operation of the safe\nroom will help determine the population served. (For example, if the safe room is adjacent to a\nschool or other large public building, will it be open to the neighboring population during the\nday, evening, and night?)\n3-10"}, {"page_number": 87, "text": "Wind Hazard Mitigation\n0.5-mile\nProposed Safe Room\nFigure 3.1: <PERSON><PERSON> proposed safe room location with 0.5-mile radius\n(Source: USGS Survey Map Data from 2011 aerial photograph from Google)\nThe second step is to determine the population that might use the safe room. Using the example\nshown in Figure 3.1, assume a safe room is adjacent to a school. The school population may be\nobtained from the school administration, State, or a national database (e.g.,\nhttp://www.schooldatadirect.org/). The school population includes not only the students, but also\nthe faculty, staff, volunteers, and visitors.\nIf the safe room will only be open to the school, then the adjacent population (typically\nresidential) does not need to be considered. If the safe room will be open to the neighboring\npublic, then the population within the 0.5-mile radius needs to be estimated.\nOne way to estimate the population within the 0.5-mile radius is to make use of data from the\nU.S. Census Bureau (http://www.census.gov/). In some cases, the State may have Web sites with\n3-11"}, {"page_number": 88, "text": "Wind Hazard Mitigation\ntools that can facilitate population calculations. For example, the Missouri Census Data Center\n(http://mcdc.missouri.edu/websas/caps.html) developed an online GIS-based tool that uses the\n2000 U.S. Census data to provide estimates of the population within a 0.5-mile radius of a\nlocation selected by a user. For areas that have experienced significant growth since the 2000\nCensus, users can alternatively count the number of houses within the 0.5-mile radius. The\npopulation within the 0.5-mile radius then can be estimated by multiplying the number of houses\nby the average household occupancy for the community or county. Average occupancy can be\nobtained from the U.S. Census Bureau, shown in Figure 3.2.\nFigure 3.2: Sample of U.S. Census Bureau data showing average household size\nIf the proposed safe room is located in a school, adjustments may be needed to avoid double-\ncounting children already included in the school population and counting adults working outside\nthe 0.5-mile radius. Adults working outside the radius are presumed to be absent from the\n3-12"}, {"page_number": 89, "text": "Wind Hazard Mitigation\ntargeted population during daylight hours and should therefore not be included in the population\nthat would use the safe room in daytime hours.\nThe following example provides a method to determine the residential daytime population within\nthe 0.5-mile radius of a school safe room:\n Assume there are 100 houses within 0.5 mile of a proposed school safe room\n U.S. Census data shows 3.18 people per house (Figure 3.2)\n (3.18 people/house) x (100 houses) = 318 people\n To subtract the children already in the school, remove the population that are less than 18\nyears old. Use U.S. Census data to determine the percentage of population over 18 years\n(Figure 3.3). In this example, 68.8 percent are 18 and over.\n (318 people) x (0.688) = 219 people\n Adjust the population for people that work outside the area during the day. Information\nrelated to this can be determined local employment records, or US Census Bureau data\n(refer to Economic Characteristics for your local area). For this example, assume that\nlocal employment records show that an estimated 75 percent work outside the 0.5-mile\nradius.\n (219 people) x (1.0 - 0.75) = 55 people\nFigure 3.3: Sample of U.S. Census Bureau data showing population 18 years and over\n Therefore, for this example, the residential daytime population that may use the proposed\nsafe room is estimated as 55 people. This number is then added to the school population\nto estimate the safe room maximum occupancy during the daytime. Additional sources of\noccupancy data are described in the dynamic help function of the Tornado Safe Room\nmodule software.\n3-13"}, {"page_number": 90, "text": "Wind Hazard Mitigation\n3.2.2 Calculating Gross and Usable Area\nThere are two types of areas that must be calculated for the Tornado Safe Room module: gross\narea and usable area. The gross area is the total area of the safe room. It should be shown on the\nproposed safe room design drawings. The usable area is the gross area minus the area of\nobstructions such as columns, partitions and walls, fixed or movable objects, furniture, or other\nequipment and features placed in the safe room. These values are related to each other and are\noften calculated incorrectly. This section describes each of these values and provides detail on\nusable area, which is the area typically miscalculated.\nMost safe room subapplications contain conceptual design floor plans and use the guidelines in\nFEMA 361, Design and Construction Guidance for Community Safe Rooms (Second Edition,\nAugust 2008) to calculate usable area. Specific calculations for the exact usable area may be\nprovided, but are typically not available at this stage in the mitigation grant process.\nAn accurate calculation of the usable area is especially important for multi-use safe rooms, such\nas classrooms and offices. The usable area should not include unused or areas that are normally\nlocked such as mechanical rooms, storage closets, or offices. FEMA 361 provides the following\nguidelines to calculate the usable area:\n Areas of concentrated furnishings or fixed seating: Areas where the furnishings cannot be\neasily moved, including bathrooms, locker rooms, weight rooms, and auditoriums with\nfixed seating. To account for the furnishings, reduce the gross floor area of such rooms\nby a minimum of 50 percent.\n Areas of unconcentrated furnishings and without fixed seating: Areas where furniture can\nbe moved, such as classrooms and offices. Reduce the gross floor area of such areas by a\nminimum of 35 percent.\n Areas of open plan furnishings and without fixed seating: Reduce the gross floor area of\nsuch areas by a minimum of 15 percent.\nRefer to Table 7 below for acceptable reductions to gross area for the purpose of calculating the\nusable area for a multi-purpose safe room.\nTable 7: Typical Gross Area Reductions by Room Use\nRoom Description Gross Area Reduction (%)\nSchool Gymnasium 15\nSchool Classroom 35\nOffice (unlocked) 35\nBathroom 50\nLocker Room 50\nMechanical Room 100\nStorage Room 100\nNOTE: Auditorium (with fixed seating): For Occupancy, use the total seating capacity\nplus the open spaces, with proper area reduction per FEMA 361.\n3-14"}, {"page_number": 91, "text": "Wind Hazard Mitigation\n3.2.3 Calculating the Maximum Occupancy\nOnce the target population has been identified and estimated, target population count must be\nchecked against the maximum occupancy or capacity of the safe room to determine whether the\nsafe room has adequate space or must be adjusted to meet the need. It is recommended that the\nsafe room be designed (i.e., sized) to accommodate the target population based on the\nrequirements of FEMA 361 (refer also to Section 3.2.2). If the safe room is sized for the target\npopulation in accordance with FEMA 361, the target population is entered in the Tornado Safe\nRoom module as the maximum occupancy. However, if the size of the safe room is already\ndetermined, the analyst will need to determine the number of people that will fit in the proposed\nsafe room based on FEMA 361 and compare that estimate with the target population within the\n0.5-mile radius. In this case, the target population is not used in the BCA Tornado Safe Room\nmodule; instead, the population that can be accommodated by the safe room is entered into the\nmodule.\nThe minimum floor space requirements for safe room occupants are shown in Table 8.\nTable 8: Maximum Occupant Density for Tornado Community Safe Rooms\nMinimum Recommended Usable Floor Area\nTornado Safe Room Occupant (sf per person)\nStanding or Seated 5\nWheelchair-bound 10\nBedridden 30\n(Source: Table 3-1, FEMA 361, Design and Construction Guidance for Community Safe Rooms, Second Edition,\nAugust 2008.)\n3.2.4 Example: Determining Required Usable Area from Maximum Occupancy\nIn the following example, the proposed maximum occupancy is known and the user is\ncalculating the required usable area. Assuming the proposed occupancy is 350 people, calculate\nthe minimum usable area required.\n1. Calculate how many wheelchair spaces are required. Using guidance in FEMA 361, one\nwheelchair space (10 sf/person) is needed for every 200 occupants. Therefore 350 occupants\nwill require two wheelchair spaces. The result is a maximum occupancy of 348 standing or\nseated occupants and two wheelchair occupants.\n2. Calculate the required usable area to accommodate the proposed occupancy.\na) (348 standing occupants) x (5 sf/person) = 1,740 sf required\nb) (Two wheelchair occupants) x (10 sf/person) = 20 sf required\nc) Required total usable area needed = 1,740 sf + 20 sf = 1,760 sf\nThe next step is to calculate the gross and usable area of the safe room. Assume the building\nfootprint is as shown in Figure 3.4.\n3-15"}, {"page_number": 92, "text": "Wind Hazard Mitigation\nBathroom\n10 feet x 20 feet\nClassroom\n50 feet x 30 feet\nMechanical Room\n10 feet x 8 feet\nFigure 3.4: Example safe room floor plan\nIn this example, the total gross area is 1,780 sf, calculated by adding the area of the rooms shown\nin Figure 3.4:\n Bathroom is 10 feet x 20 feet = 200 sf\n Mechanical Room is 10 feet x 8 feet = 80 sf\n Classroom is 50 feet x 30 feet = 1,500 sf\nUsing the total gross area of 1,780 sf, calculate the usable area using FEMA 361 guidance as\nfollows:\n Bathroom (50% reduction) = (200 sf) x (0.50) = 100 sf\n Mechanical Room (100% reduction) = (80 sf) x (1.0) = 80 sf\n Classroom (35% reduction) = (1,500 sf) x (0.35) = 525 sf\nThe total area reductions equal 705 sf. Therefore, the total usable area is calculated as:\n Gross Area – Total Reductions = Total Usable Area\n 1,780 sf – 705 sf = 1,075 sf\nThe final step is to compare the proposed usable area with the required usable area. For the\nexample problem, the calculated usable area of 1,075 sf is less than the required usable area of\n1,760 sf and therefore not enough space is provided for the targeted population.\nThe “Proposed Usable Area” of 1,075 sf would provide enough space for only 213 occupants.\nUsing FEMA 361 requirements of one wheelchair space for every 200 occupants, the 213 can be\nunderstood to mean:\n Total occupants = 213 people\n 2 wheelchair bound occupants x 10 sf/person = 20 sf\n 211 standing or seated occupants x 5 sf/person = 1,055 sf\n3-16"}, {"page_number": 93, "text": "Wind Hazard Mitigation\nFor this example, there would be two possible choices:\n1. Redefine the maximum occupancy as 213 people.\n2. Redesign the safe room to a larger size to accommodate the proposed maximum occupancy\nof 350 people. Redesigning the safe room would be recommended in this example case since\nthe safe room is for a school and the school population (students, teachers, staff, and visitors)\nis greater than 213.\n3-17"}, {"page_number": 94, "text": "Incorporating Loss of Services for Critical Public Facilities\nSECTION FOUR INCORPORATING LOSS OF SERVICES FOR CRITICAL PUBLIC\nFACILITIES\nAll FEMA BCA Version 4.5.5 modules count loss of function benefits for public facilities (i.e.,\npublic library or city hall) in a hazard situation where that facility can no longer provide its\nservices to the general public. For most public/nonprofit sector buildings, the value of services\nlost when the building becomes unusable due to a hazard event is calculated by assuming that\nservices are worth what the public pays to provide the services. This value is based on the annual\noperating budget of the agency providing the service. However, the BCA software treats critical\nfacilities (i.e., fire stations, hospital emergency rooms, and police stations) differently from those\npublic facilities due to their importance to life-safety/rescue services to the community in the\naftermath of a disaster.\nWhile varying FEMA definitions may exist, for the purposes of the BCA software and analysis,\ncritical facility service types include fire stations, hospital emergency rooms, and police stations.\nThe BCA software estimates a societal cost for the loss of such critical facilities based on the\nservice population and assesses the societal benefits of maintaining the critical facility in the\naftermath of a disaster. The critical facility analysis is therefore based on the estimate of the\npopulation served by both the critical facility and the impact a disaster would have on that\nfacility. Note that if the analysis is claiming the loss of critical services benefits, the loss of\nfunction value will need to be closely analyzed. In some cases claiming loss of critical services\nbenefits and loss of function benefits is double counting.\nThe BCA software requires specific information regarding critical facilities in order to calculate\nbenefits from mitigation projects related to reduction of loss of service. The following\ninformation is required and is often misunderstood by users of the BCA software and is\nexplained in more detail in this section:\n Number of people served by the critical facility\n Distance in miles between the critical facility and the alternate source of equivalent\nservices\n Type of area served\n Number of police serving in the aftermath of a hazard event\nFire Stations\nThe analysis performed by the BCA software for the specific loss of service for a Fire Station is\nbased on the cost to society of a temporary loss of function of a Fire Station with respect to\nhuman injuries and mortality, direct financial loss to property, and indirect losses. The analysis\nfactors in the distance to the next closest fire station that would provide protection for the\ngeographical area normally serviced by former station. Fire station facility types include\nfirefighting, search and rescue, public shelter, and emergency medical services if they are located\nin the same facility.\n Case 1: Loss of fire services only\n Use only the loss of critical services function in the software\n4-1"}, {"page_number": 95, "text": "Incorporating Loss of Services for Critical Public Facilities\n Case 2: Loss of fire services located in buildings with additional noncritical services (e.g.,\ncity halls, other government buildings, etc.)\n Use the loss of critical services function for fire and the annual operating budget\napplicable to all other noncritical services lost excluding fire\nHospital Emergency Rooms\nThe analysis performed by the BCA software of the specific loss of service for a hospital is\nbased on the cost to society of a temporary loss of function of a hospital’s emergency department\nwith respect to extra travel time to an alternative hospital and any additional waiting time at that\nalternative hospital, with the potential cost in lives resulting from that situation. Similar to the\nFire Station analysis, Hospital analysis factors in the distance to the next closest hospital that\nwould treat people normally treated by the former hospital.\n4-2"}, {"page_number": 96, "text": "Incorporating Loss of Services for Critical Public Facilities\n Case 1: Loss of the emergency room only\n Use the loss of critical service function in the software\n Case 2: Loss of services (e.g., intensive care, radiology, exam rooms, patient care wards,\netc.) excluding the emergency room\n Use the portion of the annual operating budget applicable to the services lost and not\nthe loss of critical services function\n Case 3: Loss of both the emergency room and other services outside of the emergency\nroom\n Use the loss of critical services function for the emergency room and the annual\noperating budget applicable to the other services lost excluding the emergency room\nPolice Stations\nPolice Station analysis is based on the cost to society of a temporary loss of function of a Police\nStation from the perspective of how a reduced police presence would affect the population of\nthat area. This analysis is based on the difference of a normally staffed police force and that same\nreduced force available in a post-disaster situation in the identical geographic area.\n Case 1: Loss of police services only\n Use only the loss of critical services function in the software\n Case 2: Loss of police services located in buildings with additional noncritical services:\n(e.g., city halls, other government buildings, etc.)\n Use the loss of critical services function for police and the annual operating budget\napplicable to all other noncritical services lost excluding police\n4-3"}, {"page_number": 97, "text": "Incorporating Loss of Services for Critical Public Facilities\n4.1.1 Accessing the Critical Facility Function with Flood Module\n1. On the Structure Information screen, select “No” in response to the question “Is the building\nResidential?” Note that access in the Hurricane Wind module is similar to that described\nhere.\n4-4"}, {"page_number": 98, "text": "Incorporating Loss of Services for Critical Public Facilities\n4.1.2 Accessing the Critical Facility Function with DFA Module\n1. On the Type of Service screen, select Non Residential Buildings facility type for loss of\nfunction. Note that access in the Earthquake modules is similar to that described here.\n4.1.3 Determining the Number of People Served by a Critical Facility\nAnalyzing the societal impact of the loss of a critical facility on the population it serves is done\nby comparing the distance of the critical facility to the population it serves with the distance of\n4-5"}, {"page_number": 99, "text": "Incorporating Loss of Services for Critical Public Facilities\nthat population to the next closest critical facility (with equivalent services). The analysis\nassumes the next closest critical facility would be called upon to expand their service range in the\nevent of a disaster resulting in the loss of the first critical facility. The value entered for the\npopulation served by each critical facility is strictly the population served by that critical facility,\nnot the population of an entire city or county with multiple critical facilities. The special services\nprovided by each facility should be specified.\nInformation regarding the number of people served by a critical facility (and the alternate critical\nfacility) can be obtained from the municipality, city/community officials, facility operations\nmanagers, or documents such as annual reports. If these methods are unproductive, a reasonable\nalternate methodology may be used; if an alternative approach is used, a description of that\napproach must be included in the subapplication.\nMost large cities are separated into Districts and detailed population information for each District\nis generally available. However, since hospitals are typically not operated by cities, the\npopulation served by each may be a complicated value to determine. Cities may have multiple\nhospitals, and some hospitals serve multiple communities. Some information, such as the number\nof beds and number of doctors per patient, may be found in annual reports for the hospital or on\nthe hospital Web site. Contact the hospital to obtain their estimate on per capita served.\nCommunity planners and/or community officials may also be able to provide these estimates.\nExample: Hospital in Montgomery County, MD\nFigure 4.1 shows an example of a District’s distribution within Montgomery County, MD, and\nthe hospital within that District. In this example, only one hospital is shown on the map, and a\nuser could assume the number of people served by this critical facility, outside of available\ninformation from the hospital itself, would be the population of District 3 (Figure 4.2).\n4-6"}, {"page_number": 100, "text": "Incorporating Loss of Services for Critical Public Facilities\nFigure 4.1: Hospital example - locations of critical facilities in Montgomery County, MD District 3\n(source: http://www.montgomerycountymd.gov/csltmpl.asp?url=/content/council/mem/district_map.asp)\nFigure 4.2: Hospital example - population for Montgomery County District 3 population\n(source: http://www.montgomerycountymd.gov/content/council/redistricting/Handouts/adjmoco_adjpop.pdf)\n4-7"}, {"page_number": 101, "text": "Incorporating Loss of Services for Critical Public Facilities\n4.1.4 Determining the Distance (in miles) between Critical Facilities\nFacility operations managers or municipal officials can supply information regarding the location\nof the alternate facility and the distance (in miles) between critical facilities. Distance (in miles)\nis the shortest distance travelling by vehicle along roadways. Local maps or GPS software may\nbe used as documentation of the distance. Generally, mapping software can search fire stations,\nhospitals, and police stations in a geographic area to determine the location of the nearest\nalternative critical facility (Figure 4.3).\nFigure 4.3: Hospital example - nearest alternative critical facility\n(source: http://www.montgomerycountymd.gov//content/gis/images/gallery/councildistrict1.pdf)\nMapping software can also usually get directions from location A to location B along suggested\nroutes of shortest travel distance or estimated time as shown in Figure 4.4, distance from hospital\nA to hospital B.\n4-8"}, {"page_number": 102, "text": "Incorporating Loss of Services for Critical Public Facilities\nFigure 4.4: Hospital example - distance from Hospital A to Hospital B\nExample: Hospital in Montgomery County, MD\nUsing the example of the hospital in Montgomery County, MD, the following data can be input\nto the BCA Flood module.\n1. On the Buildings page, enter the number of people served by the hospital (197,661, per\nFigure 4.2)\n2. Enter the number of miles to the nearest alternative hospital (10.5 miles, per Figures 4.3 and\n4.4)\n3. Enter the number of people served by the alternative hospital (185,462, per Figure 4.2)\n4-9"}, {"page_number": 103, "text": "Incorporating Loss of Services for Critical Public Facilities\n4.1.5 Determining the Type of Area Served by Fire and Police Stations\n4.1.5.1 Fire\nUsers must select the type of area served by a fire station, which is categorized as urban,\nsuburban, rural, or wilderness. The definition of each category is based on the “Urban Influence”\ncoding system used by the United States Department of Agriculture (USDA) and the Office of\nManagement and Budget (OMB). These codes take into account county population size, degree\nof urbanization, and adjacency to a metropolitan area or areas. The categories are defined as\nfollows:\n Urban: Counties with large (more than 1 million residents) or small (less than 1 million\nresidents) metropolitan areas\n Suburban: Micropolitan (with an urban core of at least 10,000 residents) counties\nadjacent to a large or small metropolitan area\n Rural: Non-core counties adjacent to a large or small metropolitan area (with or without\ntown)\n Wilderness: Non-core counties not adjacent to micropolitan counties (with or without\ntown)\nOne such reference for determining the type of area served includes the USDA Data Set: Urban\nInfluence Codes available at: http://www.ers.usda.gov/Data/UrbanInfluenceCodes/.\n4.1.5.2 Police\nUsers of the BCA Flood module must enter the type of area served by a police station. The user\nmust select between metropolitan, city, or rural.\nBased on data tables from the Federal Bureau of Investigation (FBI 2006), crime statistics are\nsorted into three statistical areas:\n4-10"}, {"page_number": 104, "text": "Incorporating Loss of Services for Critical Public Facilities\n Metropolitan\n Cities outside metropolitan areas\n Nonmetropolitan counties\nUsers should select “Metropolitan” for police stations that serve a principal city or urbanized\narea with a population of at least 50,000 inhabitants. The area served includes the principal city,\nthe county in which the city is located, and other adjacent counties that have, as defined by the\nOMB, a high degree of economic and social integration with the principal city and county as\nmeasured through commuting.\nUsers should select “City” only if the area served by the police station is a city outside of a\nmetropolitan area.\nUsers will not generally select “Rural” as it applies to those areas that are outside of the\nmetropolitan area and composed of mostly unincorporated areas.\n4.1.6 Determining the Number of Police Officers Serving the Same Area in the Aftermath of\na Disaster\nUsers must enter the number of police serving in the same area in the aftermath of a disaster.\nThis value is best estimated by the operations managers, the municipality, city/community\nofficials, or documents such as emergency response or continuity of operations plans. If these\nmethods are unproductive, a reasonable alternate methodology can be used to determine the\nnumber of police officers. If an alternative methodology is used, the subapplication material\nshould include all assumptions used in the calculations.\n4-11"}, {"page_number": 105, "text": "Available Technology Aids\nSECTION FIVE AVAILABLE TECHNOLOGY AIDS\nThe purpose of this section is to provide information about publically available tools that can be\nused to obtain information needed to complete the DFA, Flood and Tornado modules. FEMA\ndoes not endorse these tools over similar products and does not require that they be used.\nThe tools described in this section include Google Earth and the Hazus software. There are\nseveral free Google Earth utilities, including the FEMA National Flood Hazard Layer (NFHL)\nand the USGS Streamflow Data layer. There are also several useful utilities that are available\nthrough Google Earth if a license is purchased; these include utilities for demographics, parcel\ndata, and daily traffic counts. Table 9 shows the types of information that can be obtained using\neach tool.\nTable 9: Applicability of Technology in BCA\nGoogle Earth - Google Earth –\nHazus\nData Type Free Pro License\n(Section 5.1.1) (Section 5.1.2) (Section 5.1.3)\nFlood Hazard Information X X\nRoad/ Bridge Information X X\nSafe Room Radius X\nU.S. Demographics X\nBuilding Information X X\n5.1.1 Using Free Layers in Google Earth\nThe documentation tools that are discussed in this section are layers of the Google Earth\napplication (NFHL, USGS Streamflow Data, and Spatial Calculations) and the Google Earth Pro\nlicense. For Google Earth utilities, Google Earth must be installed on the user’s computer, and\nthe user must have a high-speed Internet connection. Note that the screenshots shown in Section\n5.1.1 were taken from Google Earth Pro (a licensed version of the software described in Section\n5.1.2), but these functions are available on the free version of Google Earth.\n5.1.1.1 National Flood Hazard Layer\nAccording to FY11 HMA Unified Guidance (FEMA, 2010a), “The required documentation\ndepends upon the nature of the proposed project and may include: proposed schematics,\ndrawings or sketches, photographs, maps, sections of hazard maps, a Flood Insurance Study\n(FIS), or a FIRM” and “subapplicants should identify the proposed project location on a map.”\nThe FEMA NFHL is a Google Earth utility that can be used to provide data for applications\nprepared for submittal to the HMA Program. A basic knowledge of Google Earth and FEMA\nflood hazard information is recommended for users of this tool. FEMA NFHL: View Custom\nCombinations of FEMA Flood Hazard Information Using Google Earth, available at\nhttp://www.fema.gov/library/viewRecord.do?id=3289, is an excellent resource for the NFHL.\n5-1"}, {"page_number": 106, "text": "Available Technology Aids\nSee https://hazards.fema.gov/femaportal/wps/portal/NFHLWMSkmzdownload for downloading\ninstructions.\nThe FEMA NFHL can be used for proposed riverine and/or coastal flooding mitigation projects\nto obtain the proposed project location, hazard type, and BFE. The NFHL also provides the\nnearest cross section or transect, which can be used to find information in the FIS. In the Map\nService Center FIRMette, the index and FIRM panel number must be searched to find the\nproposed project location, but in the NFHL, a map pin can be attached to the proposed project\nlocation and the FIRM panel number can then be obtained by zooming in on the location.\nAnother difference is that FIRMettes are in gray-scale, while the NFHL is in color and includes\nother landmarks (see Figure 5.1).\nFigure 5.1: Comparison of NFHL (left) and FEMA Map Service Center FIRMette (right)\n5-2"}, {"page_number": 107, "text": "Available Technology Aids\nThe NFHL indicates whether a proposed project location has flood hazard data.\n1. Select the Status of Digital Flood Hazard Data Coverage “high altitudes option” in the left\npanel (Figure 5.2).\nFigure 5.2: NFHL high altitudes option\n5-3"}, {"page_number": 108, "text": "Available Technology Aids\n2. Zoom to a medium altitude (Figure 5.3) to view the SFHAs, other zones, floodways,\ncommunities, and the FIRM and LOMR boundaries. These areas, zones, and boundaries are\nimportant in locating the appropriate FIRM and associated FIS.\nFigure 5.3: NFHL medium altitude areas, zones, and boundaries\n3. Zoom to a low altitude to see BFEs, cross-sections, coastal transects, hydraulic and flood\ncontrol structures, and areas affected by a Letter of Map Amendment (LOMA) or Letter of\nMap Revision (LOMR).\n5-4"}, {"page_number": 109, "text": "Available Technology Aids\n4. Using the NFHL allows the user to delineate the proposed project location within the SFHA,\nthe associated FIRM number, the BFE, and the appropriate cross-section (or transect) to use\nwhen referencing the associated FIS, as shown in Figure 5.4.\nFigure 5.4: Proposed project location on NFHL\n5.1.1.2 USGS Streamflow Data\nThe USGS Streamflow Data layer in Google Earth may help the user find the stream gage closest\nto the proposed project location, and finding the closest stream gage may help determine a\nrecurrence interval for storm event(s) related to the proposed mitigation projects location (see\nalso Section 2.2.2).\n1. Users can obtain Google Earth Streamflow KML files at\nhttp://waterwatch.usgs.gov/new/?id=real&sid=w__kml. USGS stream gage data in a keyhole\nmarkup language (KML) format.\n5-5"}, {"page_number": 110, "text": "Available Technology Aids\n2. In Google Earth, select “real-time USGS.krmz” to view a real-time USGS stream gage map\nthat is color-coded for flow conditions, as shown in Figure 5.5.\nFigure 5.5: USGS streamflow gages\n5-6"}, {"page_number": 111, "text": "Available Technology Aids\n3. Click on the stream gage symbol to displays the station number, station name, and stream\nflow and stage data, as shown in Figure 5.6.\nFigure 5.6: USGS station number display\n4. Click on the station number in the pop-up window to access the USGS Web site and up-to-\ndate information regarding all stream flow data collected at the site.\nFor details regarding obtaining historical stream gage data and determining recurrence\nintervals from this data, refer to Section 2.1.2.\n5.1.1.3 Spatial Calculations\nThe Google Earth Ruler feature shown in Figure 5.7 can be used to calculate and document\nrequired distances such as detour route and distance for use in the DFA module and a 0.5-mile\nradius for use in the Tornado Safe Room module.\nFigure 5.7: Google Earth Pro toolbar\n5-7"}, {"page_number": 112, "text": "Available Technology Aids\nDetour Route and Distance\nIn the Value of Services: Roads/ Bridges portion of the BCA DFA module, the user must enter\nthe facility description, estimated number of one-way traffic trips per day, additional time per\none-way trip due to detour, number of additional miles (see screenshot) by providing maps\nshowing the location of the road closure and the proposed detour route. The proposed location of\nthe road closure and the detour route with distance can be drawn using the Google Earth “Show\nRuler” feature in the top tool bar (see Figure 5.7)\nCalculate the proposed location of the road closure and the distance of the detour route by\nhighlighting a detour path using Length: Miles. A screen shot of the detour and the Ruler pop-up\nbox indicating the length of the detour can be attached as acceptable documentation (see\nFigure 5.8).\n5-8"}, {"page_number": 113, "text": "Available Technology Aids\nFigure 5.8: Proposed detour and distance\nSafe Room Radius\nIn identifying the Safe Room Structure type in the Tornado Safe Room module, the user is asked\nthe size (radius, in miles) of the community that will use the safe room and the predominant\nstructure type(s) that people will leave to go to the safe room. Users are asked to estimate a\nradius around the safe room location. A 0.5-mile radius or 5-minute walking distance is an\nacceptable default value per the FY11 HMA Unified Guidance (FEMA, 2010a).\n5-9"}, {"page_number": 114, "text": "Available Technology Aids\nUsers are asked to provide a copy of a radius map using aerial photography showing the\nproposed safe room location and radius. Additionally, the user is asked to select the two\npredominant structure types the target population within the radius would reside in if they do not\nuse the safe room. Both inputs can be documented and justified using a radius map showing\nstructure detail. A radius map can be obtained using the Ruler feature in Google Earth.\nThe Ruler feature may be used to draw a circle having a 0.50-mile radius from the proposed\nproject’s location to determine the structures that would benefit from a safe room. Two 0.50-mile\npaths should be drawn using the Ruler feature, as shown in Figure 5.9, and a circle should be\ndrawn to connect the end points using drawing software.\n5-10"}, {"page_number": 115, "text": "Available Technology Aids\nFigure 5.9: Using Google Earth Pro to determine 0.50-mile radius from the project location\n5.1.2 Using Google Earth Pro License\nWith an investment in a Google Earth Pro license, additional inputs are available through Google\nEarth, including U.S. demographics, U.S. parcel data (which includes residential square footage\nand year built, and commercial annual revenue and other pertinent commercial information) and\nU.S. daily traffic counts. Since Web-based sources are acceptable documentation, information\nprovided through the Google Earth Pro license is a potential resource.\nTo access the U.S. layers, the user should click the expand symbol next to the Earth Pro (U.S.)\nfolder in the Layers panel to the side of the 3D viewer and select the desired layer(s), as shown in\nFigure 5.10.\nFigure 5.10: Data layers available in Google Earth Pro (U.S.)\n5-11"}, {"page_number": 116, "text": "Available Technology Aids\nU.S. Demographics\nThe U.S. Demographics layer can be used to determine the average household size and to\ncalculate the population served (Safe Room module), as shown in Figure 5.11.\nFigure 5.11: U.S. demographics using Google Earth Pro\nU.S. Parcel Data\nSimilarly, certain residential and commercial information can be obtained from the U.S. Parcel\nData layer, which is considered acceptable documentation. For residential structures, the data\nlayer can be used to determine residential square footage (for the Flood module) and year built\n(for the DFA module), as shown in Figure 5.12.\n5-12"}, {"page_number": 117, "text": "Available Technology Aids\nFigure 5.12: U.S. parcel data using Google Earth Pro\nThe Automated Value Model (AVM) used by Google Earth Pro to value properties is not\nacceptable for calculating the BRV. AVMs such as that used by Google Earth are statistical\ncomputer programs that use real estate information such as comparable sales, property\ncharacteristics, tax assessments, and price trends to provide an estimate of value for a specific\nproperty. Acceptable documentation of the BRV includes tax records or tax cards; property\nappraisals from a building inspector or local contractor; estimates from an architect, engineer, or\nlocal building official; and documented data from a national cost-estimating guide.\nFor commercial structures, the U.S. Parcel Data layer can be used to determine the annual\noperating budget and details such as the number of beds in a hospital.\nU.S. Daily Traffic Counts\nThe U.S. Daily Traffic Counts layer available in Google Earth Pro provides an easy way to\ndetermine the average annual daily traffic count for a proposed road, as shown in Figure 5.13.\n5-13"}, {"page_number": 118, "text": "Available Technology Aids\nFigure 5.13: Daily traffic counts using Google Earth Pro\nThe estimated number of one-way traffic trips per day is expressed as a traffic count that\ntypically includes the total number of vehicles that pass a given point in both directions. Though\nunlikely, the average daily traffic (ADT) could be reported for some divided roadways for each\ndirection separately. Such ADTs should be indicated as directional. Unless Google Earth\nindicates otherwise, users can assume the ADT is for vehicles that pass a given point in both\ndirections.\nThe ADT value can be entered into the BCA software for a proposed detour. For road or bridge\nlosses that do not have detours, the number of daily trips should be based on the number of one-\nway trips (equivalent to the ADT), and the delay time should be 12 hours per one-way trip. Such\nlosses should be documented using maps that clearly show that no detour is available. In\naddition, no additional miles due to delay can be counted.\n5.1.3 Using Hazus for Flood Analysis\nThe FEMA Hazards U.S. (Hazus) model is a national model that uses standardized\nmethodologies for estimating potential losses from earthquakes, floods, and hurricanes. Hazus\nuses GIS technology to compare hazard distributions and their probabilities with property\nlocations to provide estimates of potential losses. Information about obtaining the Hazus\nsoftware and user and technical manuals is available at the FEMA Web site (search for Hazus\nUser or Technical Manuals for a particular version, e.g., MR2, MR5, at http://www.fema.gov/).\n5-14"}, {"page_number": 119, "text": "Available Technology Aids\nWhile originally developed primarily as a regional planning and disaster management tool,\nHazus is increasingly being used in local planning, including screening mitigation grant\napplications. However, Hazus and FEMA BCA methodologies have some fundamental\ndifferences, and only certain Hazus information is considered acceptable for a FEMA BCA.\nThe following subsections provide information on the acceptable and unacceptable uses of Hazus\nmethodologies for a FEMA BCA.\nThe acceptable uses of Hazus information are (see Section 5.1.3.1):\n Depth-damage functions (Flood module)\n Contents values (Flood module)\n Displacement values (Flood module)\n Flood module loss calculations (DFA module)\nThe unacceptable uses of Hazus information are (see Section 5.1.3.2):\n Level 1 analysis\n Level 2 and Level 3 analyses for census blocks\n Level 2 and Level 3 analyses for user-defined facilities\n5.1.3.1 Acceptable Uses of Hazus\nDepth-Damage Functions\nDuring the development of BCA Version 4.5.5, DDFs included in Hazus Major Release 2 (MR2)\nwere incorporated into the FEMA BCA Flood module for damage to building, contents,\ndisplacement, and loss of function. These DDFs are based on curves originally developed by the\nUSACE and the Flood Insurance Administration (FIA). In addition, FEMA developed several\nother non-residential DDFs for BCA Version 4.5.5.\nIn the Flood module, the user can select USACE DDFs, FIA DDFs, or enter custom DDFs (see\nSection 2) on either the Residential Structure Information or Non-Residential Structure\nInformation screen. The DDFs available are based on the selections the user made on the\nprevious screen (Structure Information) about structure type (residential vs. non-residential),\nnumber of stories, and basement type.\n5-15"}, {"page_number": 120, "text": "Available Technology Aids\n1. Users conducting a Flood module analysis typically use the default USACE Generic DDF\nrather than a FIA DDF.\n2. To use a DDF that is available in Hazus but not available in the Flood module, the user must\nselect Custom as the Depth-Damage Type and enter the DDF as shown in the screenshot\nbelow.\n5-16"}, {"page_number": 121, "text": "Available Technology Aids\n3. User must enter a total of four DDFs (building, contents, displacement, and loss of function).\nFor non-residential buildings, the same DDF can be used for displacement and loss of\nfunction for buildings whose occupants cannot temporarily relocate while flood repairs are\ncompleted, such as industrial companies.\nContents Values\nDuring the development of BCA Version 4.5.5, Hazus MR2 contents values were used to\ndevelop default values for different building types, as listed in Table 10.\n5-17"}, {"page_number": 122, "text": "Available Technology Aids\nTable 10: Hazus MR2 Default Contents Value Based on Percentage of Structure Value\nHazus Occupancy Hazus Occupancy Class\nNo. Class Code Description Contents Value (% of BRV)\nResidential\n1 RES1 Single Family Dwelling 50\n2 RES2 Mobile Home 50\n3 RES3 Multi Family Dwelling 50\n4 RES4 Temporary Lodging 50\n5 RES5 Institutional Dormitory 50\n6 RES6 Nursing Home 50\nCommercial\n7 COM1 Retail Trade 100\n8 COM2 Wholesale Trade 100\n9 COM3 Personal and Repair Services 100\n10 COM4 Professional/Technical/Business 100\nServices\n11 COM5 Banks 100\n12 COM6 Hospital 150\n13 COM7 Medical Office/Clinic 150\n14 COM8 Entertainment & Recreation 100\n15 COM9 Theaters 100\n16 COM10 Parking 50\nIndustrial\n17 IND1 Heavy 150\n18 IND2 Light 150\n19 IND3 Food/Drugs/Chemicals 150\n20 IND4 Metals/Minerals Processing 150\n21 IND5 High Technology 150\n22 IND6 Construction 100\nAgriculture\n23 AGR1 Agriculture 100\nReligion/Non-Profit\n24 REL1 Church/Membership Organization 100\nGovernment\n25 GOV1 General Services 100\n26 GOV2 Emergency Response 150\nEducation\n27 EDU1 Schools/Libraries 100\n28 EDU2 Colleges/Universities 150\nThe exception to these defaults is when users select residential USACE Generic DDFs. The BCA\nsoftware uses 100% of the BRV for the contents replacement value as the default when USACE\nGeneric DDFs are selected because the content-to-structure value ratio is already incorporated in\nthe contents DDF.\nWhen conducting a Flood module analysis, the user normally uses the default contents values\nprovided by the BCA software. The default contents values are based on the DDF selection\n(residential or non-residential/primary use, number of stories, basement type, and default or\ngeneric). However, in some situations, the primary building use for non-residential buildings\n5-18"}, {"page_number": 123, "text": "Available Technology Aids\ndoes not match a Hazus occupancy class (e.g., schools). For example, the BCA software does not\nhave college or university (Hazus Occupancy Class EDU2) as a non-residential option.\nTherefore, it is acceptable for the user to enter a user-defined contents value based on the Hazus\ndefaults from Table 10.\nDisplacement (Monthly and One-Time) Values\nFor residential buildings (typically single family), the Flood module uses a default value of\n$1.44/sf/month as the default displacement cost on the Residential Structure Information screen.\nThe default residential displacement cost includes both monthly (rental) and one-time\n(disruption) costs.\nHazus provides additional acceptable default values for both monthly and one-time displacement\ncosts for other residential and non-residential structure types, as shown in Table 11.\nTo calculate monthly displacement costs for other residential building types (e.g., mobile home,\napartments), the user should select the User-Entered ($/month) radio button (screenshot above)\nto override the default displacement value, and then follow these steps:\n1. From Table 11, select the most appropriate residential occupancy class and obtain the\ndisplacement cost (rental cost) from Column A.\n2. Multiply the displacement cost from Column A of Table 11 ($/sf/month) by the building area\n(sf) to calculate the displacement cost ($/month).\n3. Adjust this value from the 2008 value to the current year by using the consumer price index\n(www.bls.gov).\n4. Enter the cost into the field labeled User-Entered ($/month).\n5-19"}, {"page_number": 124, "text": "Available Technology Aids\nTable 11: Hazus MR3 Displacement Costs (2008 Values)\nFor non-residential buildings, the Flood module does not have a default value for displacement\ncosts ($/month). Therefore, a similar calculation can be performed using Table 11 and entered on\nthe Non-Residential Structure Information screen shown below.\n5-20"}, {"page_number": 125, "text": "Available Technology Aids\nColumn A in Table 11 shows the monthly rental costs, per sf, for various occupancy classes.\nThese values may be used as FEMA standard values for the monthly displacement cost. To\ncalculate monthly displacement costs for non-residential buildings, these steps should be\nfollowed:\n1. Select the most appropriate occupancy class and obtain the corresponding displacement cost\n(rental cost) from Column A of Table 11.\n2. Multiply the displacement cost from Column A of Table 11 ($/sf/month) by the total building\narea (in sf) to calculate the displacement cost ($/month).\n3. Adjust this value from 2008 value to current year by using the consumer price index\n(www.bls.gov).\n4. Enter the cost into the field labeled Displacement Cost ($/month) shown in the screenshot\nabove.\nNote that in this non-residential building example, the total building size is 10,000 sf and the first\nfloor area is 5,000 sf. The first floor area (5,000 sf) is entered in the Total Size of Building cell\nbecause the default building and contents DDFs for non-residential buildings apply only to first\nfloor damages. However, the total building area (10,000 sf) is used to calculate displacement\ncosts because the total floor area is occupied as shown below.\n5-21"}, {"page_number": 126, "text": "Available Technology Aids\nFor this example the monthly displacement cost for a retail clothing building is calculated as\nfollows:\nMonthly displacement cost = [rental cost ($/sf/month)] x [total area (sf)]\n= ($1.25/sf/month) x (10,000 sf)\n= $12,500/month\nTable 11 is also used to calculate one-time displacement costs. To calculate one-time\ndisplacement costs, also known as disruption costs, for both residential and non-residential\nstructures, these steps should be followed:\n1. Determine the appropriate occupancy class from Table 11.\n2. Multiply the associated disruption cost ($/sf) from Column B of Table 11 by the total\nbuilding area (in sf) to calculate the one-time displacement cost.\n3. Adjust this value from 2008 value to current year by using consumer price index\n(www.bls.gov).\n4. Enter the cost into the field labeled One-Time Displacement Costs ($).\nThe default residential displacement cost ($1.44/sf/month), which applies to single-family\ndwellings, already incorporates one-time displacement costs. Do not include one-time\ndisplacement costs if the default residential displacement cost is selected.\nFor the example shown in the screenshot above, the one-time displacement cost for the Retail-\nClothing building is calculated as:\nOne-time displacement costs = [disruption cost ($/sf)] x [total area (sf)]\n= ($1.17/sf) x (10,000 sf)\n= $11,700\nFlood Module Loss Calculations in DFA Module\nWhile the previous three subsections have focused on the use of information obtained from\nHazus to use in the Flood module, but some Hazus information is acceptable for the DFA\nmodule. The general approach is for the user to use the DDFs and other loss calculations in the\nFlood module to derive values that can be entered in the DFA module. See Section 2.1.4 for\ndetails on how to implement this method.\n5.1.3.2 Unacceptable Uses of Hazus\nHazus Level 1 Analysis\nThe Hazus Level 1 Analysis is defined in the Hazus MR5 User Manual (available at\nhttp://www.fema.gov/library/viewRecord.do?id=4454) as the simplest type of Hazus analysis,\nwhich is based primarily on data provided with the software. The assumption is that the user is\nusing (1) Hazus-based H&H study methods and (2) the built environment as modeled from\nHazus-based General Building Stock (GBS) data, usually at the U.S. census block level.\nHowever, neither of the Hazus approaches for H&H and GBS is considered acceptable for BCA.\nHazus-based H&H normally uses 30- or 10-meter resolution topographic data (USGS digital\n5-22"}, {"page_number": 127, "text": "Available Technology Aids\nelevation model data), which do not meet BCA requirements for topographic or H&H modeling.\nFor the Flood module, topographic data must satisfy a minimum vertical resolution equivalent to\n2-foot contour data. H&H methods need to match the standards used for FEMA detailed flood\nstudies, as described in the Guidelines and Specifications for Flood Hazard Mapping Partners\n(FEMA 2003), and specifically the lists of acceptable H&H models. See Numerical Models\nMeeting the Minimum Requirements of the National Flood Insurance Program (FEMA 2010b)\navailable at http://www.fema.gov/plan/prevent/fhm/en_modl.shtm.\nHazus Level 1 analysis census block-based GBS data also do not satisfy BCA requirements. This\napproach does not model actual buildings but uses statistical data to approximate the\ncharacteristics and damages across entire census blocks. This approach does not provide the level\nof detail needed for BCA.\nHazus Level 2 and 3 Analyses for Census Blocks\nHazus Levels 2 and 3 Analyses are defined by the Hazus MR5 User Manual as advanced\nanalyses in Hazus. Level 2 is usually considered as updating Hazus datasets with better local\ndata. The Level 3 analysis uses models that are external to Hazus, such as engineering and\neconomic models.\nFor GBS data, the Levels 2 and 3 Analyses are available for updating the default Hazus datasets\nsuch as the Hazus Comprehensive Data Management System tool. However, as mentioned in the\nprevious section, census block-based GBS methods in Hazus are not acceptable for BCA because\nactual buildings characteristics are not modeled. BCA methods for mitigation projects such as\nacquisition and elevation are based on building-by-building analysis, and Hazus census block-\nbased approach are insufficient for this purpose.\nHazus Level 2 and 3 Analysis for User-Defined Facilities\nAnother Hazus approach is the User-Defined Facilities (UDF) analysis. In the UDF analysis, the\nuser enters building point locations and structural information such as Hazus occupancy type,\nfirst floor height above grade, building and contents costs, number of stories, and foundation\ntype. Hazus then uses GIS methods to query flood depth grids and Hazus DDFs to estimate\nbuilding and contents losses. The UDF analysis can be useful for screening large numbers of\nbuildings to determine the buildings that may be the best candidates for a BCA.\nHowever, the fundamental differences between the Hazus and BCA approaches that make the\nHazus UDF analysis unacceptable for BCA are as follows:\n Topographic data requirements. BCA requires a minimum vertical detail equivalent to 2-\nfoot contours for mapping. Any flood depth grids derived in Hazus that do not use\ntopographic data with that detail level are unacceptable for BCA.\n H&H modeling methods. The second set of differences concern H&H modeling methods.\nLevel 1 Hazus-based H&H are not considered acceptable for BCA according to FEMA’s\nGuidelines and Specifications for Flood Hazard Mapping Partners (FEMA 2003)\nbecause of the generalized attributes of the data.\nHowever, an experienced Hazus user could develop flood depth grids using Hazus Level\n2 or 3 analysis that overcome the differences in topographic data requirements and H&H\nmodeling methods. The Hazus Level 2 analysis allows the user to import flood depth\ngrids developed from external models, such as the FEMA-acceptable model Hydrologic\n5-23"}, {"page_number": 128, "text": "Available Technology Aids\nEngineering Centers River Analysis System (HEC-RAS). Hazus also has the Flood\nInformation Tool program that allows development of flood depth grids from cross-\nsectional data from external models. It is possible for a Hazus user to develop flood depth\ngrids based on FEMA-acceptable models and with sufficient topographic details to\nsatisfy the BCA requirements.\n Loss calculation methods. The primary reason the Hazus UDF analysis is considered\nunacceptable for BCA is the methods used to calculate losses for each recurrence interval\nand annualized loss. Although the Hazus UDF table and Hazus depth grids can be\nconfigured to return the same percent building damage (based on the same flood depth\nand building DDF) as the BCA, the way that BCA uses these values to derive annualized\nloss is different than Hazus. Hazus does not have the capability to calculate annualized\nloss when user-defined depth grids are used. Hazus MR4 does include an annualized loss\ncalculation, but it applies only to scenarios that use Hazus-based H&H methods.\nIn addition, the Hazus annualized calculation uses extrapolated 2- and 5-year recurrence\ninterval events. Even these limited annualized loss calculations were removed for Hazus\nMR5. In contrast, the BCA uses the stream bed elevation to interpolate losses for events\nbelow the 10-year event. See the Flood Full Data Module Methodology Report for more\ninformation (available as part of the BCA Resource Kit at\nhttp://www.bchelpline.com/BCAToolkit/resources_flood.html).\n Demolition threshold. When building DDFs exceed the demolition threshold in the BCA\nsoftware, the building damage is set to 100% and all other DDFs are set to their\nmaximum for that structure type. Hazus does not use any demolition threshold as part of\ncensus block GBS or the UDF analysis.\nThere is currently no way to use a Hazus UDF analysis to replace a BCA Flood module analysis.\nAlthough the Hazus UDF analysis could be made to match certain parts of the BCA calculations,\nthere are fundamental calculation differences between the Flood module and Hazus software. If a\nuser has data such as detailed structure characteristics, a HEC-RAS analysis, and detailed flood\ndepth and water surface elevations grids, these data could be used for the Flood module rather\nthan trying to make Hazus approximate what the BCA software does by default.\n5-24"}, {"page_number": 129, "text": "References\nSECTION SIX REFERENCES\nBCA Helpline. http://www.bchelpline.com/. Accessed June 2011.\nBenefit-Cost Analysis Resource Kit. Hazard: Flood.\nhttp://www.bchelpline.com/BCAToolkit/resources_flood.html. Accessed June 2011.\nBureau of Labor Statistics. http://bls.gov/. Accessed _June 2011.\nCampoli, J<PERSON>, et al., 2001. Above and Beyond, Visualizing Changes in Small Town and Rural\nAreas. American Planning Association Press, Chicago, IL.\nCouncil of Chief State School Officers, School Matters, 2011. School enrollment data.\nhttp://www.schooldatadirect.org/. Accessed June 2011.\nFederal Bureau of Investigation. 2006 Uniform Crime Reporting [UCR] Program.\nhttp://www2.fbi.gov/ucr/cius2006/data/table_05.htmlAccessed October 2008.\nFEMA (Federal Emergency Management Agency). 2003 with updates through 2009. Guidelines\nand Specifications for Flood Hazard Mapping Partners.\nhttp://www.fema.gov/library/viewRecord.do?id=2206. Accessed June 2011.\nFEMA. 2006 Hazus-MH MR3 Flood Technical Manual\nFEMA. 2008. Design and Construction Guidance for Community Safe Rooms (FEMA 361).\nSecond Edition, August 2008.\nFEMA. 2010a. Hazard Mitigation Assistance Unified Guidance: Hazard Mitigation Grant\nProgram, Pre-Disaster Mitigation Program, Flood Mitigation Assistance Program,\nRepetitive Flood Claims Program, Severe Repetitive Loss Program, June 1, 2010\nFEMA. 2010b. Numerical Models Meeting the Minimum Requirements of the National Flood\nInsurance Program. http://www.fema.gov/plan/prevent/fhm/en_modl.shtm. Accessed\nNovember 2011.\nFEMA. 2010c Hazus-MH MR5 Flood Technical Manual\nFEMA. 2011a. Hazus FEMA’s Methodology for Estimating Potential Losses from Disasters.\nhttp://www.fema.gov/plan/prevent/hazus/index.shtm. Accessed June 2011.\nFEMA. 2011b. Using the National Flood Hazard Layer Web Service (WMS) in Google Earth.\nhttps://hazards.fema.gov/femaportal/wps/portal/NFHLWMSkmzdownload. Accessed\nJune 2011.\nLake County Illinois GIS and Mapping Division. 2010. Topographic Map Gallery.\nhttp://oldapps.lakecountyil.gov/gis/TopoGallery/MapsInqMapName.asp. Accessed April\n2011.\nMissouri Census Data Center. Circular Area Profiles.\nhttp://mcdc.missouri.edu/websas/caps.html. Accessed _June 2011.\nMontgomery County Maryland. Montgomery County Council.\nhttp://www.montgomerycountymd.gov/csltmpl.asp?url=/content/council/mem/district_m\nap.asp. Accessed June 2011.\n6-1"}, {"page_number": 130, "text": "References\nMontgomery County Maryland. Montgomery County Council. Council District 1.\nhttp://www.montgomerycountymd.gov//content/gis/images/gallery/councildistrict1.pdf.\nMontgomery County Maryland. Montgomery County Council. Council District Population\nChange: 2000 to 2010 (adjusted) Montgomery County, Maryland.\nhttp://www.montgomerycountymd.gov/content/council/redistricting/Handouts/adjmoco_a\ndjpop.pdf.\nNOAA. Photograph used courtesy of <PERSON>. www.srh.noaa.gov. Accessed June 2011.\nNorth Carolina Chapter of the American Society for Photogrammetry and Remote Sensing\n(NCASPRS). 2010. Flood Analysis and Flood Mapping: Where is the First Floor?\nwww.ncasprs.com/pub/FirstFloorElevations_ncss.pptx. Accessed April 2010.\nSpinell Homes. 2010. Models. http://www.spinellhomes.com/models. Accessed April 2010.\nU.S. Census Bureau. 2009. Community Population Estimates.\nhttp://factfinder.census.gov/servlet/ACSSAFFFacts?_event=&geo_id=16000US2462850\n&_geoContext=01000US%7C04000US24%7C16000US2462850&_street=&_county=po\nolesville&_cityTown=poolesville&_state=04000US24&_zip=&_lang=en&_sse=on&Act\niveGeoDiv=&_useEV=&pctxt=fph&pgsl=160&_submenuId=factsheet_1&ds_name=null\n&_ci_nbr=null&qr_name=null&reg=null%3Anull&_keyword=&_industry=). Accessed\nJune 2011.\nU.S. Geological Survey. 2011. Google Earth Streamflow KML Files.\nhttp://waterwatch.usgs.gov/new/?id=real&sid=w__kml. Accessed June 2011.\nUnited States Department of Agriculture (USDA). Data Sets. 2010.\nhttp://www.ers.usda.gov/Data/. Accessed June 2011.\n6-2"}]}