<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBCS Professional Analysis Service - ComplianceMax V74</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            font-size: 12px;
        }
        
        .container {
            width: 100vw;
            height: 100vh;
            display: grid;
            grid-template-rows: 55px 1fr 35px;
            gap: 0;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 18px;
            font-weight: 700;
        }
        
        .badges {
            display: flex;
            gap: 8px;
        }
        
        .badge {
            padding: 3px 8px;
            border-radius: 12px;
            color: white;
            font-size: 9px;
            font-weight: 600;
        }
        
        .badge.policy { background: #e74c3c; }
        .badge.database { background: #27ae60; }
        .badge.cbcs { background: #f39c12; }
        
        .main-content {
            background: white;
            display: grid;
            grid-template-columns: 1.6fr 1.6fr 2.8fr;
            gap: 0;
            overflow: hidden;
        }
        
        .column {
            padding: 10px;
            overflow-y: auto;
            border-right: 1px solid #e1e8ed;
            height: 100%;
        }
        
        .column:last-child { border-right: none; }
        
        .column h3 {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 8px;
            padding-bottom: 4px;
            border-bottom: 2px solid #3498db;
            font-weight: 700;
        }
        
        .form-group {
            margin-bottom: 8px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 2px;
            font-size: 10px;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 5px 6px;
            border: 1px solid #e1e8ed;
            border-radius: 3px;
            font-size: 10px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .auto-populate-btn {
            background: #f39c12;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: 600;
            cursor: pointer;
            font-size: 9px;
            margin-top: 5px;
            width: 100%;
        }
        
        .auto-populate-btn:hover {
            background: #e67e22;
        }
        
        .cbcs-codes {
            background: #f8f9fa;
            border: 1px solid #e1e8ed;
            border-radius: 3px;
            padding: 6px;
            margin-bottom: 6px;
        }
        
        .cbcs-codes h4 {
            margin: 0 0 4px 0;
            font-size: 11px;
            color: #2c3e50;
            border-bottom: 1px solid #ddd;
            padding-bottom: 2px;
            font-weight: 700;
        }
        
        .code-item {
            display: flex;
            align-items: flex-start;
            padding: 3px;
            cursor: pointer;
            font-size: 9px;
            margin-bottom: 2px;
            border-radius: 2px;
            transition: background 0.2s;
        }
        
        .code-item:hover {
            background: #e3f2fd;
        }
        
        .code-item.selected {
            background: #e8f5e8;
            border: 1px solid #27ae60;
        }
        
        .code-item input {
            margin-right: 4px;
            margin-top: 1px;
            transform: scale(0.8);
        }
        
        .code-item label {
            cursor: pointer;
            line-height: 1.1;
            flex: 1;
        }
        
        .code-title {
            font-weight: 700;
            color: #2c3e50;
            display: block;
            margin-bottom: 1px;
        }
        
        .code-desc {
            color: #7f8c8d;
            font-size: 8px;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            font-weight: 600;
            cursor: pointer;
            font-size: 10px;
        }
        
        .btn-primary {
            background: #27ae60;
            color: white;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: #219a52;
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: 1fr;
            gap: 3px;
            margin-bottom: 8px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            font-size: 9px;
        }
        
        .checkbox-item input {
            margin-right: 3px;
            transform: scale(0.7);
        }
        
        .submit-section {
            margin-top: 20px;
            text-align: center;
        }
        
        /* Notification system styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .notification.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .notification.error {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        
        /* Scrollbar styling */
        .column::-webkit-scrollbar {
            width: 4px;
        }
        
        .column::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        .column::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }
        
        .column::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ CBCS Professional Analysis Service</h1>
            <div class="badges">
                <span class="badge policy">53K+ Policies</span>
                <span class="badge database">Phase 8 DB</span>
                <span class="badge cbcs">Auto-Population</span>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Project Information Column -->
            <div class="column">
                <h3>📋 Project Information</h3>
                
                <div class="form-group">
                    <label class="form-label">Project Name *</label>
                    <input type="text" class="form-control" id="projectName" placeholder="Enter project name" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Facility Type *</label>
                    <select class="form-control" id="facilityType" required>
                        <option value="">Select Facility Type</option>
                        <option value="building">Building</option>
                        <option value="bridge">Bridge</option>
                        <option value="road">Road/Highway</option>
                        <option value="water">Water System</option>
                        <option value="wastewater">Wastewater System</option>
                        <option value="electric">Electric Power</option>
                        <option value="school">School</option>
                        <option value="hospital">Hospital</option>
                        <option value="critical">Critical Facility</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Estimated Cost *</label>
                    <input type="number" class="form-control" id="estimatedCost" placeholder="Enter cost in USD" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Damage Date *</label>
                    <input type="date" class="form-control" id="damageDate" required>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Work Description *</label>
                    <textarea class="form-control" id="workDescription" rows="2" placeholder="Describe the work to be performed" required></textarea>
                </div>
                
                <button class="auto-populate-btn" onclick="autoPopulateCodes()">
                    🤖 Auto-Populate Codes
                </button>
            </div>
            
            <!-- Additional Details Column -->
            <div class="column">
                <h3>⚖️ Additional Details</h3>
                
                <div class="form-group">
                    <label class="form-label">FEMA Disaster Declaration</label>
                    <input type="text" class="form-control" id="femaDeclaration" placeholder="e.g., FEMA-4XXX-DR">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Environmental Concerns</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="wetlands">
                            <label for="wetlands">Wetlands/Water bodies nearby</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="floodplain">
                            <label for="floodplain">Located in floodplain</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="endangered">
                            <label for="endangered">Endangered species habitat</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Historic Preservation</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="nationalRegister">
                            <label for="nationalRegister">Listed on National Register</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="historicDistrict">
                            <label for="historicDistrict">Located in historic district</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="over50">
                            <label for="over50">Structure over 50 years old</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Special Requirements</label>
                    <div class="checkbox-group">
                        <div class="checkbox-item">
                            <input type="checkbox" id="seismic">
                            <label for="seismic">Seismic design required</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="floodResistant">
                            <label for="floodResistant">Flood-resistant design</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="ada">
                            <label for="ada">ADA compliance required</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- CBCS Codes Column -->
            <div class="column">
                <h3>🔧 CBCS Code Selection</h3>
                
                <div class="cbcs-codes">
                    <h4>AASHTO Standards</h4>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="AASHTO_LRFD_Bridge">
                        <label for="AASHTO_LRFD_Bridge">
                            <span class="code-title">AASHTO LRFD Bridge</span>
                            <span class="code-desc">Load and Resistance Factor Design</span>
                        </label>
                    </div>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="AASHTO_Highway_Geometric">
                        <label for="AASHTO_Highway_Geometric">
                            <span class="code-title">AASHTO Highway Geometric</span>
                            <span class="code-desc">Highway geometric design standards</span>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Structural Standards</h4>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="ASCE_7_16">
                        <label for="ASCE_7_16">
                            <span class="code-title">ASCE/SEI 7-16</span>
                            <span class="code-desc">Wind, seismic, snow load requirements</span>
                        </label>
                    </div>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="ACI_318_19">
                        <label for="ACI_318_19">
                            <span class="code-title">ACI 318-19</span>
                            <span class="code-desc">Concrete design standards</span>
                        </label>
                    </div>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="AISC_360_19">
                        <label for="AISC_360_19">
                            <span class="code-title">AISC 360-19</span>
                            <span class="code-desc">Steel building specifications</span>
                        </label>
                    </div>
                </div>
                
                <div class="cbcs-codes">
                    <h4>Safety & Accessibility</h4>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="NFPA_1141">
                        <label for="NFPA_1141">
                            <span class="code-title">NFPA 1141</span>
                            <span class="code-desc">Fire protection infrastructure</span>
                        </label>
                    </div>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="NFPA_NEC">
                        <label for="NFPA_NEC">
                            <span class="code-title">NFPA NEC</span>
                            <span class="code-desc">National Electrical Code</span>
                        </label>
                    </div>
                    <div class="code-item" onclick="toggleCode(this)">
                        <input type="checkbox" id="ADA_2010">
                        <label for="ADA_2010">
                            <span class="code-title">ADA 2010 Standards</span>
                            <span class="code-desc">Accessibility guidelines</span>
                        </label>
                    </div>
                </div>
                
                <div class="submit-section">
                    <button class="btn btn-primary" onclick="submitAnalysis()">
                        Submit for Professional Analysis
                    </button>
                </div>
            </div>
        </div>
        
        <div class="footer">
            ComplianceMax V74 | Phase 8 Database: 53,048 FEMA Policy Records | Auto-Population Engine Active
        </div>
    </div>

    <script>
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        function toggleCode(element) {
            const checkbox = element.querySelector('input[type="checkbox"]');
            checkbox.checked = !checkbox.checked;
            element.classList.toggle('selected', checkbox.checked);
        }
        
        function autoPopulateCodes() {
            const facilityType = document.getElementById('facilityType').value;
            const estimatedCost = document.getElementById('estimatedCost').value;
            const workDescription = document.getElementById('workDescription').value;
            
            if (!facilityType) {
                showNotification('Please select a facility type first', 'error');
                return;
            }
            
            // Send request to auto-populate API
            fetch('/api/cbcs/auto-populate', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    facilityType: facilityType,
                    estimatedCost: parseInt(estimatedCost) || 0,
                    workDescription: workDescription,
                    workCategory: 'C'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Auto-select relevant codes
                    data.auto_selected_codes.forEach(code => {
                        const checkbox = document.getElementById(code.code);
                        if (checkbox) {
                            checkbox.checked = true;
                            checkbox.closest('.code-item').classList.add('selected');
                        }
                    });
                    
                    // Show subtle notification instead of intrusive popup
                    showNotification(`Auto-populated ${data.total_codes} codes`, 'success');
                } else {
                    showNotification('Error auto-populating codes: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error connecting to auto-population service', 'error');
            });
        }
        
        function submitAnalysis() {
            // Collect all form data
            const projectData = {
                projectName: document.getElementById('projectName').value,
                facilityType: document.getElementById('facilityType').value,
                estimatedCost: document.getElementById('estimatedCost').value,
                damageDate: document.getElementById('damageDate').value,
                workDescription: document.getElementById('workDescription').value,
                femaDeclaration: document.getElementById('femaDeclaration').value
            };
            
            // Collect selected CBCS codes
            const selectedCodes = [];
            document.querySelectorAll('.code-item input:checked').forEach(checkbox => {
                selectedCodes.push(checkbox.id);
            });
            
            projectData.cbcsCodes = selectedCodes;
            
            // Submit for professional analysis
            fetch('/api/compliance-analysis/submit', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    project_data: projectData,
                    service_level: 'standard',
                    notification_email: '<EMAIL>'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showNotification(`Analysis submitted! ID: ${data.analysis_id}`, 'success');
                } else {
                    showNotification('Error submitting analysis: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error submitting analysis', 'error');
            });
        }
    </script>
</body>
</html> 