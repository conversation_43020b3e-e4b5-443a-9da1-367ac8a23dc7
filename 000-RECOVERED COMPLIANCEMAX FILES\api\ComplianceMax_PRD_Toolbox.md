# === ComplianceMax PRD Scaffold ===

## 🧭 PROJECT TITLE
ComplianceMax: Automated FEMA Public Assistance Compliance Manager

## 📌 OVERVIEW
Automate and streamline FEMA PA compliance for applicants and reviewers through real-time policy syncing, structured Compliance Pods, and intelligent document logic.

## 🧑‍💼 TARGET USERS
- State and local government recovery coordinators
- FEMA contractors and compliance officers
- Nonprofits applying for disaster aid

## 🎯 PROBLEM
Manually managing over 7,000+ conditional FEMA PA compliance rules leads to delays, funding loss, and audit risks.

## 🛠 SOLUTION
1. Document-driven AI compliance wizard
2. Compliance Pods (modular checklists mapped to FEMA categories)
3. Real-time FEMA API and XAI scraper for regulation updates
4. Built-in validation, logging, and audit support

## 🌐 KEY INTEGRATIONS
- FEMA Open API
- XAI variant rule scraper
- GROK for clause answering
- CodeRabbit for review

## ⚙️ CORE FEATURES
- 🧱 Compliance Pods (Cat A, B, C, etc.)
- 📥 Applicant upload logic
- 🔄 Auto-population of checklists
- 🧠 Semantic rule matching (LLM-enhanced)
- 🛡 Audit tracking & clause versioning

## 🧩 TECH STACK
- Cursor (IDE / DevOps / Prompter)
- Python, SQLite (or PostgreSQL), Lang<PERSON>hain (GROK base)
- JSON/Markdown for document processing
- REST API interface + local agent scripts

## ✅ DEFINITION OF DONE
- PRD is implemented in TaskMaster
- Each compliance pod matches FEMA categories
- XAI clauses sync without crashes
- Review pipeline (CodeRabbit / PR) operates cleanly

# === Folder Layout (Cursor-ready) ===

📁 /compliancemax
│
├── fema_api/
│   └── fetch_fema_data.py
├── xai_scraper/
│   ├── fetch_variants.py
│   └── classify_scrape.py
├── compliance_pods/
│   ├── base_pod.py
│   ├── cat_a.py
│   ├── cat_b.py
├── ai/
│   ├── grok_embedder.py
│   ├── semantic_matcher.py
│   └── retrain_vector_store.py
├── memory/
│   ├── cache_manager.py
│   ├── compare_to_cached.py
│   └── history_log.py
├── ui/
│   ├── pod_status_viewer.py
│   └── ui_feedback_sandbox/
├── logs/
│   └── clause_change_log.md
├── .audit_logs/
│   └── rule_inference_trail.json
├── .cursorignore
├── .gitignore
└── README.md

# === Bat Tools (Windows Utility Scripts) ===

📁 /tools
│
├── backup_chat_history.bat
@echo off
setlocal
set BACKUP_DIR=%USERPROFILE%\Desktop\Cursor_Chat_Backups\%date:/=-%_%time::=-%
mkdir "%BACKUP_DIR%"
robocopy "%APPDATA%\Cursor\globalStorage" "%BACKUP_DIR%" /E
echo Backup completed to: %BACKUP_DIR%
pause

├── launch_cursor_clean.bat
@echo off
set CURSOR_PATH=%LOCALAPPDATA%\Programs\Cursor\Cursor.exe
start "" "%CURSOR_PATH%" --disable-extensions

├── purge_cursor_logs.bat
@echo off
del /q "%APPDATA%\Cursor\logs\*.*"
echo Cursor logs cleared.
pause

├── cursor_bat_cheatsheet.txt
📘 CURSOR BAT FILE GUIDE

1. 🔁 **Back up your Cursor chat history**
   - File: `backup_chat_history.bat`
   - What it does: Copies your `globalStorage` chat files into a timestamped folder on your Desktop.
   - How to run: Right-click > Run as Administrator

2. 🧼 **Launch Cursor with extensions off**
   - File: `launch_cursor_clean.bat`
   - What it does: Opens Cursor with `--disable-extensions` to avoid crashing
   - Use when: Cursor is slow, freezing, or acting buggy

3. 🗑 **Purge Cursor logs**
   - File: `purge_cursor_logs.bat`
   - What it does: Deletes Cursor’s local logs to free up memory and fix error loops
   - Use when: Log files are bloated or causing memory errors

✅ TIP: Save all `.bat` files in a folder like `C:\Users\<USER>\Documents\CursorTools`
✅ TIP: Make shortcuts for quick right-click admin launching.
