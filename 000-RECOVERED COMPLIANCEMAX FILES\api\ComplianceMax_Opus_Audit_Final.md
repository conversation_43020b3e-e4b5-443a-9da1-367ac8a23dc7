## ComplianceMax: Multi-Layered Deep Audit Template for Claude Opus 4

### Objective
Perform a comprehensive multi-layered analysis of the ComplianceMax V74 FEMA compliance application using Claude Opus 4’s extended thinking mode, memory files, and tool integration. This system-wide audit evaluates architecture, performance, completeness, compliance, and AI augmentation potential to ensure production readiness.

### Compliance Requirement
- All file operations (>200 lines) MUST use the Enhanced Edit Tool (`EDIT FILE TOOL/enhanced-edit-tool-1.0.0.vsix`) with a 95%+ success rate, per `RULES/MANDATORY_AGENT_RULES_ENHANCED_EDIT_TOOL.md`.
- Standard tools (`edit_file`, `search_replace`) are prohibited due to 0% success rate.
- Never use PowerShell; use `cmd.exe` for terminal commands to comply with project constraints.

### Cursor Workflow Setup
To address Cursor’s lack of file tree access for Claude Opus 4:
1. Open the ComplianceMax V74 project folder in Cursor.
2. Tag specific files in prompts (e.g., `@app/web_app_clean.py`).
3. Generate `project_context.json` to summarize the project structure for context:

```python
import os, json
project_root = "path/to/ComplianceMax"
file_context = {}
for root, _, files in os.walk(project_root):
    for file in files:
        file_path = os.path.join(root, file)
        with open(file_path, 'r', errors='ignore') as f:
            file_context[file_path] = f.read()[:1000]
with open('project_context.json', 'w') as f:
    json.dump(file_context, f)
```

4. Create a `.cursorrules` file in the project root to enforce project rules:
```
Use Enhanced Edit Tool for all file edits >200 lines.
Avoid PowerShell; use cmd.exe.
Tag files in prompts for context.
.aiignore: exclude node_modules/, fema_docs_enhanced_v2.db, *.log
```
5. Configure Cursor terminal to `cmd.exe`: Settings > terminal.integrated.defaultProfile.windows
6. Test changes: `curl http://localhost:5000/api/fema/health`

---

### Layers of Analysis (10 Total)
Each includes a prompt, validation strategy, and directive to use Enhanced Edit Tool:

#### Layer 1: Project Decomposition
Break down major files (e.g., `web_app_clean.py`, `fema_api_client.py`, `templates/`) into subsystems. List unused logic (e.g., RSS parsing).

#### Layer 2: Functional Evaluation
Map logic to FEMA PAPPG clauses via database queries. Link routes to compliance outcomes.

#### Layer 3: Completeness Check
Flag missing features (e.g., rule variance handling, document versioning). Prioritize by compliance risk.

#### Layer 4: Performance Optimization
Identify slow or bloated query logic. Use parallel compute to simulate load with 50k+ records.

#### Layer 5: Security & Integrity
Audit input validation, OWASP compliance, and missing authentication/audit logging logic.

#### Layer 6: Interdependencies & Edge Cases
Test JSON corruption, upload size, API failures. Generate dependency maps and CLI test scripts.

#### Layer 7: Scalability
Test 100k-record performance, simulate 50 concurrent users, and recommend multi-tenant design.

#### Layer 8: Compliance Logic Traceability
Tag validation logic to FEMA rules using `PAPPG_TAGS.md`. Reference database entries.

#### Layer 9: AI Augmentation
Detect current AI usage. Recommend GROK bots, RAG for rule lookup, predictive risk flagging.

#### Layer 10: DX/UX
Evaluate code clarity, template logic, test coverage, user interface structure.

---

### Phase 2: Action Plan Generation
Prioritize tasks by compliance risk, dev effort, and system impact. Output as Markdown, JSON, or CSV.

---

### Metrics Tracking
Track Opus 4 using the following:
- Success rate (e.g., "Layer 1 = 95% complete")
- Time per layer ("Layer 2 = 1 session")
- Clarity and minimal rework
- Log updates in `SESSION_SUMMARY_YYYYMMDD.md`
- Document changes in `CHANGELOG.md`

---

### Notes from Eureka Moments / Historical Fixes
Include lessons learned and errors now codified:

- 🔧 The Enhanced Edit Tool is mandatory due to failure of `edit_file` on large files (1900+ lines).
- 🔐 `REFERENCE DOCS/` folder is read-only; all files within are protected and version-referenced only.
- ⚠️ Never delete files. Archive only if absolutely necessary to avoid breaking historical dependency.
- 📁 A `.gitignore` pattern mistake was corrected when you flagged missing `REFERENCE DOCS` snapshots.
- 📜 Compliance Pods required manual discovery—non-standard HTML template was missing in early audits.
- 📊 `project_context.json` creation was missed by default agents until it was manually defined.
- 💣 PowerShell automation broke workflows and was flagged manually. `cmd.exe` is now enforced.
- 🧠 Boilerplate doc comments (missing in early versions) are now required for agent understanding.
- 📂 Initial version lacked changelog and session logs; this is now a critical audit requirement.
- 🧪 Health check endpoint (`/api/fema/health`) was not functioning due to hotfix misplacement—now standard practice to run `fema_hotfix.py` before launch.

---

### Grok Summary of Session Wins (2025-06-14)
- 🧾 Finalized a robust Opus 4 audit prompt for the ComplianceMax V74 project.
- 🛠️ Developed a `.cursorrules` file and terminal override for `cmd.exe` use.
- ⚠️ Reaffirmed that PowerShell must not be used in automation; broken in Cursor.
- 📂 Verified and enforced `.gitignore` behavior to preserve `REFERENCE DOCS`.
- 💡 Clarified doc string expectations and required boilerplate comment hygiene.
- 🧠 Manual discovery of logic gaps (like Compliance Pods) led to improved test templates.
- 📑 Built and exported the full audit guide in `.md` format for Opus onboarding.

---

### Final Tip
Use Opus' extended memory features to track which clauses, endpoints, and templates correspond. This avoids circular reviews and missing logic when working across layers.

Then, compile all responses into a single exportable `Opus_Audit_Report.md` and `ComplianceMax_Dev_Roadmap.md` with diffs and completed validations.
