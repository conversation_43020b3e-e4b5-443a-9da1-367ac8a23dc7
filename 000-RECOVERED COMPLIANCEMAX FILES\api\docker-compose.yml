version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: compliancemax-postgres
    environment:
      POSTGRES_DB: compliancemax
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - compliancemax-network
    restart: unless-stopped

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: compliancemax-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - compliancemax-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # ComplianceMax Application
  app:
    build: .
    container_name: compliancemax-app
    environment:
      - POSTGRESQL_URL=********************************************/compliancemax
      - REDIS_URL=redis://redis:6379/0
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./storage:/app/storage
      - ./cache:/app/cache
      - ./temp:/app/temp
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - compliancemax-network
    restart: unless-stopped

  # Document Processing Worker
  worker:
    build: .
    container_name: compliancemax-worker
    environment:
      - POSTGRESQL_URL=********************************************/compliancemax
      - REDIS_URL=redis://redis:6379/0
    command: python -m celery worker -A app.worker --loglevel=info
    volumes:
      - ./uploads:/app/uploads
      - ./storage:/app/storage
      - ./cache:/app/cache
      - ./temp:/app/temp
    depends_on:
      - postgres
      - redis
    networks:
      - compliancemax-network
    restart: unless-stopped

  # Next.js Frontend (if using the ALL NEW APP)
  frontend:
    build: ./ALL NEW APP
    container_name: compliancemax-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    networks:
      - compliancemax-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  compliancemax-network:
    driver: bridge 