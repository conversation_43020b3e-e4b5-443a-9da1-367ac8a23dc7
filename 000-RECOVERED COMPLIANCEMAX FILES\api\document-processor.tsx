"use client";

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion } from 'framer-motion';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Eye,
  Download,
  X
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ProcessedDocument {
  id: string;
  name: string;
  extractedText: string;
  markdown: string;
  pages: number;
  confidence: number;
  femaCategory?: string;
  metadata: {
    fileSize: number;
    fileType: string;
    processingTime: number;
  };
}

interface DocumentProcessorProps {
  onDocumentProcessed?: (document: ProcessedDocument) => void;
  projectId?: string;
  allowMultiple?: boolean;
}

export function DocumentProcessor({ 
  onDocumentProcessed, 
  projectId,
  allowMultiple = true 
}: DocumentProcessorProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedDocuments, setProcessedDocuments] = useState<ProcessedDocument[]>([]);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [enableFEMADetection, setEnableFEMADetection] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<ProcessedDocument | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    setIsProcessing(true);
    setError(null);
    setProcessingProgress(0);

    try {
      const results: ProcessedDocument[] = [];
      
      for (let i = 0; i < acceptedFiles.length; i++) {
        const file = acceptedFiles[i];
        setProcessingProgress((i / acceptedFiles.length) * 100);

        const formData = new FormData();
        formData.append('file', file);
        formData.append('detectFEMA', enableFEMADetection.toString());
        if (projectId) {
          formData.append('projectId', projectId);
        }

        const response = await fetch('/api/documents/process', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process document');
        }

        const result = await response.json();
        if (result.success) {
          results.push(result.document);
          onDocumentProcessed?.(result.document);
        }
      }

      setProcessedDocuments(prev => [...prev, ...results]);
      setProcessingProgress(100);
      
      // Clear progress after a short delay
      setTimeout(() => setProcessingProgress(0), 2000);

    } catch (error) {
      console.error('Document processing error:', error);
      setError(error instanceof Error ? error.message : 'Failed to process documents');
    } finally {
      setIsProcessing(false);
    }
  }, [enableFEMADetection, projectId, onDocumentProcessed]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: allowMultiple,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.gif', '.tiff', '.bmp'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['.pptx'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'text/html': ['.html'],
      'text/markdown': ['.md'],
      'text/plain': ['.txt']
    },
    disabled: isProcessing
  });

  const removeDocument = (id: string) => {
    setProcessedDocuments(prev => prev.filter(doc => doc.id !== id));
    if (selectedDocument?.id === id) {
      setSelectedDocument(null);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getFEMACategoryColor = (category?: string) => {
    if (!category || category === 'Uncategorized') return 'secondary';
    return 'default';
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Document Processor
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Options */}
          <div className="flex items-center space-x-2">
            <Checkbox 
              id="fema-detection" 
              checked={enableFEMADetection}
              onCheckedChange={(checked) => setEnableFEMADetection(checked as boolean)}
            />
            <label htmlFor="fema-detection" className="text-sm font-medium">
              Auto-detect FEMA categories
            </label>
          </div>

          {/* Drop Zone */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer
              ${isDragActive ? 'border-blue-500 bg-blue-50 dark:bg-blue-950' : 'border-gray-300 hover:border-gray-400'}
              ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              {isProcessing ? (
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              ) : (
                <Upload className="h-8 w-8 mx-auto text-gray-400" />
              )}
              
              <div>
                {isProcessing ? (
                  <p className="text-sm text-gray-600">Processing documents...</p>
                ) : isDragActive ? (
                  <p className="text-sm text-blue-600">Drop files here to process</p>
                ) : (
                  <div>
                    <p className="text-sm text-gray-600">
                      Drag & drop documents here, or click to select
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Supports: PDF, Images, DOCX, PPTX, XLSX, HTML, MD, TXT
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Processing Progress */}
          {processingProgress > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Processing...</span>
                <span>{Math.round(processingProgress)}%</span>
              </div>
              <Progress value={processingProgress} className="h-2" />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Processed Documents */}
      {processedDocuments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Processed Documents ({processedDocuments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="list" className="w-full">
              <TabsList>
                <TabsTrigger value="list">List View</TabsTrigger>
                <TabsTrigger value="details">Detailed View</TabsTrigger>
              </TabsList>
              
              <TabsContent value="list" className="space-y-3">
                {processedDocuments.map((doc) => (
                  <motion.div
                    key={doc.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border rounded-lg p-4 space-y-3"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <CheckCircle className="h-5 w-5 text-green-500" />
                        <div>
                          <h4 className="font-medium">{doc.name}</h4>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{doc.pages} pages</span>
                            <span>•</span>
                            <span className={getConfidenceColor(doc.confidence)}>
                              {Math.round(doc.confidence * 100)}% confidence
                            </span>
                            <span>•</span>
                            <span>{Math.round(doc.metadata.fileSize / 1024)} KB</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {doc.femaCategory && (
                          <Badge variant={getFEMACategoryColor(doc.femaCategory)}>
                            {doc.femaCategory}
                          </Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSelectedDocument(doc)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeDocument(doc.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    {doc.extractedText && (
                      <div className="bg-gray-50 dark:bg-gray-900 rounded p-3">
                        <p className="text-sm text-gray-600 mb-2">Extracted Text Preview:</p>
                        <p className="text-sm line-clamp-3">
                          {doc.extractedText.substring(0, 200)}...
                        </p>
                      </div>
                    )}
                  </motion.div>
                ))}
              </TabsContent>
              
              <TabsContent value="details">
                {selectedDocument ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{selectedDocument.name}</h3>
                      <Button
                        variant="outline"
                        onClick={() => setSelectedDocument(null)}
                      >
                        Close
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 border rounded">
                        <div className="text-2xl font-bold">{selectedDocument.pages}</div>
                        <div className="text-sm text-gray-500">Pages</div>
                      </div>
                      <div className="text-center p-3 border rounded">
                        <div className={`text-2xl font-bold ${getConfidenceColor(selectedDocument.confidence)}`}>
                          {Math.round(selectedDocument.confidence * 100)}%
                        </div>
                        <div className="text-sm text-gray-500">Confidence</div>
                      </div>
                      <div className="text-center p-3 border rounded">
                        <div className="text-2xl font-bold">
                          {Math.round(selectedDocument.metadata.fileSize / 1024)}
                        </div>
                        <div className="text-sm text-gray-500">KB</div>
                      </div>
                      <div className="text-center p-3 border rounded">
                        <div className="text-2xl font-bold">
                          {selectedDocument.metadata.processingTime}
                        </div>
                        <div className="text-sm text-gray-500">ms</div>
                      </div>
                    </div>
                    
                    {selectedDocument.femaCategory && (
                      <div>
                        <h4 className="font-medium mb-2">FEMA Category</h4>
                        <Badge variant={getFEMACategoryColor(selectedDocument.femaCategory)}>
                          {selectedDocument.femaCategory}
                        </Badge>
                      </div>
                    )}
                    
                    <div>
                      <h4 className="font-medium mb-2">Extracted Text</h4>
                      <div className="bg-gray-50 dark:bg-gray-900 rounded p-4 max-h-64 overflow-y-auto">
                        <pre className="text-sm whitespace-pre-wrap">
                          {selectedDocument.extractedText}
                        </pre>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    Select a document from the list to view details
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 