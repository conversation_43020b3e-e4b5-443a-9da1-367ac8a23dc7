'use client';

import React, { useState } from 'react';
import { useApiHealth, useComplianceStats, useChecklistItems } from '@/lib/api';

export default function Dashboard() {
  const { health, loading: healthLoading, error: healthError } = useApiHealth();
  const { stats, loading: statsLoading, error: statsError } = useComplianceStats();
  const { items, loading: itemsLoading, error: itemsError } = useChecklistItems({ limit: 5 });

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-blue-600 mb-2">
          ComplianceMax V74 Dashboard
        </h1>
        <p className="text-gray-600">
          FEMA Public Assistance Compliance Management System
        </p>
      </div>

      {/* API Status */}
      <div className="bg-white p-6 rounded-lg shadow-md border">
        <h2 className="text-xl font-semibold mb-4">Backend Connection Status</h2>
        
        {healthLoading && (
          <div className="text-blue-600">🔄 Connecting to API...</div>
        )}
        
        {healthError && (
          <div className="text-red-600">❌ API Offline: {healthError}</div>
        )}
        
        {health && (
          <div className="space-y-2">
            <div className={`text-lg font-medium ${health.status === 'healthy' ? 'text-green-600' : 'text-yellow-600'}`}>
              ✅ API Status: {health.status}
            </div>
            <div className="text-sm text-gray-600">
              Version: {health.version} | Database: {health.database}
            </div>
            <div className="flex gap-2 mt-2">
              {Object.entries(health.services).map(([service, status]) => (
                <span 
                  key={service}
                  className={`px-2 py-1 rounded text-xs ${
                    status.includes('operational') || status.includes('connected') 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {service}: {status}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Statistics */}
      <div className="bg-white p-6 rounded-lg shadow-md border">
        <h2 className="text-xl font-semibold mb-4">Compliance Statistics</h2>
        
        {statsLoading && (
          <div className="text-blue-600">📊 Loading statistics...</div>
        )}
        
        {statsError && (
          <div className="text-red-600">❌ Stats Error: {statsError}</div>
        )}
        
        {stats && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.total_items}</div>
              <div className="text-sm text-gray-600">Total Items</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Object.keys(stats.by_category).length}
              </div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {Object.keys(stats.by_phase).length}
              </div>
              <div className="text-sm text-gray-600">Phases</div>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {stats.conditional_logic_items}
              </div>
              <div className="text-sm text-gray-600">IF-THEN Rules</div>
            </div>
          </div>
        )}
      </div>

      {/* Category Breakdown */}
      {stats && (
        <div className="bg-white p-6 rounded-lg shadow-md border">
          <h2 className="text-xl font-semibold mb-4">Category Breakdown</h2>
          <div className="space-y-2">
            {Object.entries(stats.by_category)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 10)
              .map(([category, count]) => (
                <div key={category} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                  <span className="font-medium">{category}</span>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                    {count}
                  </span>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Recent Items */}
      <div className="bg-white p-6 rounded-lg shadow-md border">
        <h2 className="text-xl font-semibold mb-4">Recent Compliance Items</h2>
        
        {itemsLoading && (
          <div className="text-blue-600">📋 Loading items...</div>
        )}
        
        {itemsError && (
          <div className="text-red-600">❌ Items Error: {itemsError}</div>
        )}
        
        {items && items.length > 0 ? (
          <div className="space-y-4">
            {items.map((item) => (
              <div key={item.id} className="p-4 border rounded-lg hover:bg-gray-50">
                <h3 className="font-semibold text-blue-600">
                  {item.title || 'Untitled Item'}
                </h3>
                <p className="text-gray-600 text-sm mt-1">
                  {item.description || 'No description available'}
                </p>
                <div className="flex gap-2 mt-2">
                  {item.category && (
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                      {item.category}
                    </span>
                  )}
                  {item.pappg_version && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                      {item.pappg_version}
                    </span>
                  )}
                  {item.phase && (
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                      Phase {item.phase}
                    </span>
                  )}
                </div>
                {item.trigger_condition_if && (
                  <div className="mt-2 text-xs text-gray-500">
                    <strong>IF:</strong> {item.trigger_condition_if.slice(0, 100)}...
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500">No items loaded yet.</div>
        )}
      </div>

      {/* Real-time Status */}
      <div className="bg-white p-6 rounded-lg shadow-md border">
        <h2 className="text-xl font-semibold mb-4">System Status</h2>
        <div className="grid md:grid-cols-3 gap-4 text-sm">
          <div>
            <strong>Frontend:</strong> Next.js (Running on :3333)
          </div>
          <div>
            <strong>Backend:</strong> FastAPI {health ? `(v${health.version})` : '(Checking...)'}
          </div>
          <div>
            <strong>Data Processing:</strong> {stats ? '✅ Active' : '⏳ Loading'}
          </div>
        </div>
      </div>
    </div>
  );
} 