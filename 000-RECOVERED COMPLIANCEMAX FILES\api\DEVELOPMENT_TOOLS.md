# ComplianceMax Development Tools

This document outlines development and testing tools created to help review and develop the ComplianceMax application across different platforms.

## Database Tools

### Schema Export

The `export_schema.py` script exports the database schema without any sensitive data:

```bash
python export_schema.py
```

This will create a `schema.sql` file containing all table definitions, indices, triggers, and views from the database. This is useful for:

- Understanding the data structure
- Version controlling schema changes
- Sharing database structure with team members without sharing data
- Creating new development databases

### Test Data Generator

The `create_test_data.py` script creates a test database with anonymized sample data:

```bash
python create_test_data.py
```

This will:
1. Create a new SQLite database file named `compliancemax_test.db`
2. Generate tables matching the ComplianceMax schema
3. Populate tables with realistic but anonymized test data

The test data includes:
- User accounts with test credentials
- Projects with various statuses
- Documents with metadata (but no actual file content)
- Policies categorized by type
- Application records with various compliance statuses

Use this test database for:
- Development without affecting production data
- Testing new features safely
- Reproducing issues in a clean environment
- Running automated tests

## System Health Tools

### Health Check Script

The `health_check.py` script performs a comprehensive health check of the ComplianceMax environment:

```bash
python health_check.py
```

This script checks:
- Python environment and dependencies
- Node.js environment
- Directory structure
- Database connectivity and content
- API endpoints
- Running services on expected ports
- Environment configuration files

Use this tool to:
- Quickly diagnose system issues
- Verify your development environment is correctly set up
- Check if all required components are running
- Get troubleshooting recommendations for failed checks

The health check provides color-coded output and specific recommendations based on any issues found.

## Error Logging

### Error Log Template

The `error_log_template.md` file provides a standardized format for reporting errors. When you encounter issues:

1. Copy the template to a new file named for the specific issue
2. Fill in all relevant sections
3. Share with the development team

The template ensures all necessary information is captured:
- Platform details
- Error messages
- Reproduction steps
- Environment information
- Resolution tracking

### Sample Error Log

The `sample_error_log.md` file demonstrates how to properly complete an error log. Use this as a reference when documenting issues.

## Using These Tools in Your Workflow

### For New Team Members

1. Clone the repository
2. Run `python health_check.py` to verify your environment
3. Run `python create_test_data.py` to create a local test database
4. Use the generated test credentials to log in during development

### When Encountering Errors

1. Run `python health_check.py` to identify potential issues
2. Copy `error_log_template.md` to a new file named after the issue
3. Complete all sections with detailed information
4. Submit with your pull request or issue report

### Before Committing Changes

1. Run `python export_schema.py` if you've made database changes
2. Include the updated schema.sql file in your commit
3. Document any schema changes in your commit message
4. Run `python health_check.py` to ensure all systems are operational

## Platform-Specific Notes

### Windows

- Use Python scripts in the root directory (e.g., `run.py`) to launch the application
- If SQLite CLI is needed, install from https://www.sqlite.org/download.html

### macOS/Linux

- You may need to add execute permissions to Python scripts: `chmod +x *.py`
- Database file paths may need adjustment

### Cursor IDE

- Use the integrated terminal to run these tools
- The error logging template works well with Cursor's markdown preview feature 