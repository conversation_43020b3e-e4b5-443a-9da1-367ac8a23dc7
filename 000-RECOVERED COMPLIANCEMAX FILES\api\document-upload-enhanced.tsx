"use client"

import { useState, useCallback, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useDropzone } from "react-dropzone"
import { 
  AlertCircle,
  Check,
  File,
  FileText,
  Image as ImageIcon,
  Loader2,
  Upload,
  X,
  FileArchive,
  FileSpreadsheet
} from "lucide-react"

import { formatBytes } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface DocumentUploadEnhancedProps {
  projectId: string
  stepId?: string
  onUploadComplete?: (documents: any[]) => void
}

// Create a proper type that extends File
type FileWithPreview = {
  id: string;
  name: string;
  size: number;
  type: string;
  preview?: string;
  progress: number;
  status: 'idle' | 'uploading' | 'success' | 'error';
  errorMessage?: string;
  category?: string;
  // Add a reference to the original file for upload
  file: File;
  // Add processing results
  extractedText?: string;
  femaCategory?: string;
  confidence?: number;
}

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/msword': ['.doc'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/vnd.ms-excel': ['.xls'],
  'image/jpeg': ['.jpg', '.jpeg'],
  'image/png': ['.png'],
  'application/zip': ['.zip'],
  'application/x-zip-compressed': ['.zip'],
}

const FEMA_DOCUMENT_CATEGORIES = [
  { value: "project-worksheet", label: "Project Worksheet" },
  { value: "damage-inventory", label: "Damage Inventory" },
  { value: "cost-documentation", label: "Cost Documentation" },
  { value: "procurement-documentation", label: "Procurement Documentation" },
  { value: "environmental-historic", label: "Environmental & Historic Preservation" },
  { value: "insurance-documentation", label: "Insurance Documentation" },
  { value: "permits", label: "Permits & Approvals" },
  { value: "correspondence", label: "Correspondence" },
  { value: "other", label: "Other" },
]

export function DocumentUploadEnhanced({ projectId, stepId, onUploadComplete }: DocumentUploadEnhancedProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [overallProgress, setOverallProgress] = useState(0)
  const { toast } = useToast()
  const uploadTimersRef = useRef<Record<string, NodeJS.Timeout>>({})

  const getFileIcon = (file: File) => {
    const type = file.type.toLowerCase()
    
    if (type.includes('pdf')) return <FileText className="h-6 w-6 text-red-500" />
    if (type.includes('word') || type.includes('document')) return <FileText className="h-6 w-6 text-blue-500" />
    if (type.includes('sheet') || type.includes('excel')) return <FileSpreadsheet className="h-6 w-6 text-green-500" />
    if (type.includes('image')) return <ImageIcon className="h-6 w-6 text-purple-500" />
    if (type.includes('zip')) return <FileArchive className="h-6 w-6 text-orange-500" />
    return <File className="h-6 w-6 text-gray-500" />
  }

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: FileWithPreview[] = acceptedFiles.map(file => {
      const id = Math.random().toString(36).substring(2, 11)
      
      // Create preview for images
      let preview
      if (file.type.startsWith('image/')) {
        preview = URL.createObjectURL(file)
      }
      
      return {
        id,
        name: file.name,
        size: file.size,
        type: file.type,
        preview,
        progress: 0,
        status: 'idle',
        category: undefined,
        file: file
      }
    })
    
    setFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    maxSize: 10 * 1024 * 1024, // 10MB
  })

  const removeFile = (id: string) => {
    setFiles(prev => {
      const filtered = prev.filter(file => file.id !== id)
      
      // If we have a preview URL, revoke it to avoid memory leaks
      const fileToRemove = prev.find(file => file.id === id)
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview)
      }
      
      // Clear any upload timers for this file
      if (uploadTimersRef.current[id]) {
        clearTimeout(uploadTimersRef.current[id])
        delete uploadTimersRef.current[id]
      }
      
      return filtered
    })
  }

  const setFileCategory = (id: string, category: string) => {
    setFiles(prev => 
      prev.map(file => 
        file.id === id ? { ...file, category } : file
      )
    )
  }

  const uploadFileToAPI = async (fileWithPreview: FileWithPreview) => {
    return new Promise<any>((resolve, reject) => {
      // Update to uploading status
      setFiles(prev => 
        prev.map(file => 
          file.id === fileWithPreview.id 
            ? { ...file, status: 'uploading', progress: 10 } 
            : file
        )
      )

      const formData = new FormData()
      formData.append('file', fileWithPreview.file)
      formData.append('detectFEMA', 'true') // Enable FEMA detection
      if (projectId) {
        formData.append('projectId', projectId)
      }

      fetch('/api/documents/process', {
        method: 'POST',
        body: formData,
      })
      .then(async (response) => {
        // Update progress to show processing
        setFiles(prev => 
          prev.map(file => 
            file.id === fileWithPreview.id 
              ? { ...file, progress: 70 } 
              : file
          )
        )

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to process document')
        }

        return response.json()
      })
      .then((result) => {
        if (result.success) {
          // Update to success with processed data
          setFiles(prev => 
            prev.map(file => 
              file.id === fileWithPreview.id 
                ? { 
                    ...file, 
                    progress: 100, 
                    status: 'success',
                    // Store the processed document data
                    extractedText: result.document.extractedText,
                    femaCategory: result.document.femaCategory,
                    confidence: result.document.confidence
                  } 
                : file
            )
          )
          resolve(result.document)
        } else {
          throw new Error('Processing failed')
        }
      })
      .catch((error) => {
        console.error(`Error uploading file ${fileWithPreview.name}:`, error)
        setFiles(prev => 
          prev.map(file => 
            file.id === fileWithPreview.id 
              ? { 
                  ...file, 
                  progress: 0, 
                  status: 'error',
                  errorMessage: error.message || 'Upload failed. Please try again.'
                } 
              : file
          )
        )
        reject(error)
      })
    })
  }

  const uploadFiles = async () => {
    if (files.length === 0) return
    
    // Check if all files have categories
    const uncategorizedFiles = files.filter(file => !file.category)
    if (uncategorizedFiles.length > 0) {
      toast({
        variant: "destructive",
        title: "Missing categories",
        description: "Please assign a category to all documents before uploading.",
      })
      return
    }
    
    setIsUploading(true)
    
    try {
      // Track overall progress
      const updateOverallProgress = () => {
        const totalProgress = files.reduce((sum, file) => sum + file.progress, 0)
        setOverallProgress(totalProgress / files.length)
      }
      
      // Start uploading each file
      const uploadPromises = files.map(async (file) => {
        try {
          const result = await uploadFileToAPI(file)
          updateOverallProgress()
          return result
        } catch (error) {
          console.error(`Error uploading file ${file.name}:`, error)
          updateOverallProgress()
          return null
        }
      })
      
      const results = await Promise.all(uploadPromises)
      const successfulUploads = results.filter(Boolean)
      
      if (successfulUploads.length > 0) {
        toast({
          title: "Upload complete",
          description: `Successfully processed ${successfulUploads.length} of ${files.length} documents with OCR and FEMA categorization.`,
        })
        
        if (onUploadComplete) {
          onUploadComplete(successfulUploads)
        }
        
        // Remove successful uploads from the list after a delay
        setTimeout(() => {
          setFiles(prev => prev.filter(file => file.status !== 'success'))
        }, 3000)
      }
      
      if (successfulUploads.length < files.length) {
        toast({
          variant: "destructive",
          title: "Some uploads failed",
          description: "Please check the failed uploads and try again.",
        })
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Upload failed",
        description: "There was an error uploading your documents. Please try again.",
      })
    } finally {
      setIsUploading(false)
      setOverallProgress(0)
    }
  }

  const viewProcessingResults = (file: FileWithPreview) => {
    toast({
      title: "Processing Results",
      description: `${file.femaCategory || 'No category detected'} (${file.confidence ? Math.round(file.confidence * 100) + '%' : 'No'} confidence)`,
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white font-heading">
          Document Upload
        </h3>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => setFiles([])}
            disabled={files.length === 0 || isUploading}
            className="shadow-soft hover:shadow-medium transition-all"
          >
            Clear All
          </Button>
          <Button
            size="sm"
            onClick={uploadFiles}
            disabled={files.length === 0 || isUploading}
            className="gradient-button"
          >
            {isUploading ? (
              <div className="flex items-center">
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </div>
            ) : (
              <div className="flex items-center">
                <Upload className="h-4 w-4 mr-2" />
                Upload Files
              </div>
            )}
          </Button>
        </div>
      </div>

      {/* Dropzone */}
      <div 
        {...getRootProps()} 
        className={`border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer
          ${isDragActive ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-700'}
          ${isDragAccept ? 'border-green-400 bg-green-50 dark:bg-green-900/20' : ''}
          ${isDragReject ? 'border-red-400 bg-red-50 dark:bg-red-900/20' : ''}
        `}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center text-center">
          <Upload className="h-12 w-12 text-gray-400 mb-4" />
          <p className="text-base font-medium text-gray-900 dark:text-white">
            {isDragActive 
              ? isDragReject 
                ? "Some files are not supported" 
                : "Drop files here..."
              : "Drag & drop files here or click to browse"
            }
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Supports PDF, Word, Excel, and image files (max 10MB each)
          </p>
        </div>
      </div>

      {/* Overall progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall progress</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>
      )}

      {/* File list */}
      {files.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 font-heading">
            Selected Files ({files.length})
          </h4>
          <div className="space-y-4">
            <AnimatePresence>
              {files.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-soft hover:shadow-medium"
                >
                  <div className="p-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mr-4">
                        <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-md">
                          {getFileIcon(file.file)}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate font-heading">
                              {file.name}
                            </h4>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                              {formatBytes(file.size)}
                            </p>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(file.id)}
                            disabled={file.status === 'uploading'}
                            className="p-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 transition-colors"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        </div>
                        
                        {/* File category selection */}
                        <div className="mt-3">
                          <Select
                            value={file.category}
                            onValueChange={(value) => setFileCategory(file.id, value)}
                            disabled={file.status === 'uploading'}
                          >
                            <SelectTrigger className="w-full h-8 text-xs">
                              <SelectValue placeholder="Select document category" />
                            </SelectTrigger>
                            <SelectContent>
                              {FEMA_DOCUMENT_CATEGORIES.map((category) => (
                                <SelectItem key={category.value} value={category.value} className="text-xs">
                                  {category.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        {/* Upload progress and results */}
                        {file.status !== 'idle' && (
                          <div className="mt-3">
                            <div className="flex justify-between text-xs mb-1">
                              <span className="flex items-center">
                                {file.status === 'uploading' && (
                                  <Loader2 className="h-3 w-3 mr-1 animate-spin text-blue-500" />
                                )}
                                {file.status === 'success' && (
                                  <Check className="h-3 w-3 mr-1 text-green-500" />
                                )}
                                {file.status === 'error' && (
                                  <AlertCircle className="h-3 w-3 mr-1 text-red-500" />
                                )}
                                {file.status === 'uploading' && 'Processing with OCR...'}
                                {file.status === 'success' && 'Processing complete'}
                                {file.status === 'error' && file.errorMessage}
                              </span>
                              <span>{Math.round(file.progress)}%</span>
                            </div>
                            <Progress 
                              value={file.progress} 
                              className={`h-1 ${
                                file.status === 'success' ? 'bg-green-100' : 
                                file.status === 'error' ? 'bg-red-100' : ''
                              }`} 
                            />
                            
                            {/* Show processing results */}
                            {file.status === 'success' && (file.femaCategory || file.confidence) && (
                              <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
                                {file.femaCategory && (
                                  <div className="flex items-center justify-between">
                                    <span>FEMA Category:</span>
                                    <span className="font-medium text-blue-600">{file.femaCategory}</span>
                                  </div>
                                )}
                                {file.confidence && (
                                  <div className="flex items-center justify-between">
                                    <span>Confidence:</span>
                                    <span className={`font-medium ${
                                      file.confidence >= 0.8 ? 'text-green-600' : 
                                      file.confidence >= 0.6 ? 'text-yellow-600' : 
                                      'text-red-600'
                                    }`}>
                                      {Math.round(file.confidence * 100)}%
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Preview for images */}
                    {file.preview && (
                      <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-3">
                        <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-md overflow-hidden relative">
                          <img 
                            src={file.preview} 
                            alt={file.name}
                            className="object-contain w-full h-full"
                          />
                        </div>
                      </div>
                    )}
                    
                    {/* Actions */}
                    {file.status === 'success' && file.extractedText && (
                      <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-3 flex justify-end space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => viewProcessingResults(file)}
                          className="shadow-soft hover:shadow-medium transition-all"
                        >
                          View Details
                        </Button>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  )
}