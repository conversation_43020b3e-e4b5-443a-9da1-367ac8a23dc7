/**
 * Authentication API Integration Tests
 * Following Project Policy Section 5.2 - Integration Test Focus
 * Test-First Development: These tests drive API implementation
 */

const request = require('supertest');
const { Pool } = require('pg');

// Test database configuration
const testDb = new Pool({
  host: process.env.TEST_POSTGRES_HOST || 'localhost',
  port: process.env.TEST_POSTGRES_PORT || 5432,
  database: process.env.TEST_POSTGRES_DB || 'compliancemax_test',
  user: process.env.TEST_POSTGRES_USER || 'postgres',
  password: process.env.TEST_POSTGRES_PASSWORD || 'password',
});

describe('Authentication API', () => {
  let app;
  
  beforeAll(async () => {
    // Import app after environment setup
    app = require('../../server');
    
    // Setup test database schema
    await testDb.query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        fema_registration VARCHAR(50),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      );
      
      CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT NOW()
      );
    `);
  });

  beforeEach(async () => {
    // Clean test data before each test
    await testDb.query('TRUNCATE TABLE user_sessions, users CASCADE');
  });

  afterAll(async () => {
    // Clean up test schema
    await testDb.query('DROP TABLE IF EXISTS user_sessions, users CASCADE');
    await testDb.end();
  });

  describe('POST /api/auth/register', () => {
    test('should register new user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        femaRegistration: 'FEMA-12345'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user.femaRegistration).toBe(userData.femaRegistration);
      expect(response.body.user).not.toHaveProperty('password');
    });

    test('should reject registration with invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'SecurePassword123!',
        femaRegistration: 'FEMA-12345'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('email');
    });

    test('should reject registration with weak password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Weak password
        femaRegistration: 'FEMA-12345'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('password');
    });

    test('should reject duplicate email registration', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        femaRegistration: 'FEMA-12345'
      };

      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Duplicate registration
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(409);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('already exists');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user for login tests
      await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          femaRegistration: 'FEMA-LOGIN'
        });
    });

    test('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(loginData.email);
    });

    test('should reject login with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword123!'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid credentials');
    });

    test('should reject login with non-existent email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid credentials');
    });
  });

  describe('GET /api/auth/profile', () => {
    let authToken;

    beforeEach(async () => {
      // Register and get auth token
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          femaRegistration: 'FEMA-PROFILE'
        });
      
      authToken = registerResponse.body.token;
    });

    test('should return user profile with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body.user.femaRegistration).toBe('FEMA-PROFILE');
    });

    test('should reject request without auth token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('token');
    });

    test('should reject request with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid token');
    });
  });

  describe('FEMA Integration & Auto-Population', () => {
    test('should validate FEMA registration format', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        femaRegistration: 'INVALID-FORMAT'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('FEMA registration');
    });

    test('should auto-populate user data from FEMA# lookup', async () => {
      const femaNumber = 'FEMA-12345-TX';
      
      const response = await request(app)
        .post('/api/fema/lookup-registration')
        .send({ femaNumber })
        .expect(200);

      expect(response.body).toHaveProperty('profile');
      expect(response.body.profile).toHaveProperty('state');
      expect(response.body.profile).toHaveProperty('county');
      expect(response.body.profile).toHaveProperty('eligibleDisasters');
    });

    test('should accept valid FEMA registration formats', async () => {
      const validFormats = [
        'FEMA-12345',
        'PA-12345-TX',
        '12345678'
      ];

      for (const femaReg of validFormats) {
        const userData = {
          email: `test${femaReg.replace(/[^a-zA-Z0-9]/g, '')}@fema.gov`,
          password: 'SecurePassword123!',
          femaRegistration: femaReg
        };

        const response = await request(app)
          .post('/api/auth/register')
          .send(userData);

        expect(response.status).toBe(201);
        expect(response.body.user.femaRegistration).toBe(femaReg);
      }
    });
  });

  describe('FEMA API Integration Tests', () => {
    test('should fetch current open disasters from FEMA API', async () => {
      const response = await request(app)
        .get('/api/fema/disasters/open')
        .expect(200);

      expect(response.body).toHaveProperty('disasters');
      expect(Array.isArray(response.body.disasters)).toBe(true);
      
      if (response.body.disasters.length > 0) {
        const disaster = response.body.disasters[0];
        expect(disaster).toHaveProperty('drNumber');
        expect(disaster).toHaveProperty('state');
        expect(disaster).toHaveProperty('incidentType');
        expect(disaster).toHaveProperty('declarationDate');
      }
    });

    test('should filter disasters by state/county hierarchy', async () => {
      // Test state-level filtering
      const stateResponse = await request(app)
        .get('/api/fema/disasters/state/TX')
        .expect(200);

      expect(stateResponse.body).toHaveProperty('disasters');
      
      // Test county-level filtering
      const countyResponse = await request(app)
        .get('/api/fema/disasters/county/TX/Harris')
        .expect(200);

      expect(countyResponse.body).toHaveProperty('disasters');
      expect(countyResponse.body.disasters.length).toBeLessThanOrEqual(
        stateResponse.body.disasters.length
      );
    });
  });

  describe('Geographic Hierarchy Tests', () => {
    test('should provide state list for dropdown', async () => {
      const response = await request(app)
        .get('/api/geo/states')
        .expect(200);

      expect(response.body).toHaveProperty('states');
      expect(Array.isArray(response.body.states)).toBe(true);
      expect(response.body.states.length).toBeGreaterThan(0);
      
      const state = response.body.states[0];
      expect(state).toHaveProperty('code');
      expect(state).toHaveProperty('name');
    });

    test('should provide counties for selected state', async () => {
      const response = await request(app)
        .get('/api/geo/counties/TX')
        .expect(200);

      expect(response.body).toHaveProperty('counties');
      expect(Array.isArray(response.body.counties)).toBe(true);
      
      if (response.body.counties.length > 0) {
        const county = response.body.counties[0];
        expect(county).toHaveProperty('name');
        expect(county).toHaveProperty('fipsCode');
      }
    });
  });

  describe('Backend Integration Tests', () => {
    test('should connect user to PAPPG logic workflow', async () => {
      // This test ensures our API connects to existing backend logic
      const userData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        femaRegistration: 'FEMA-INT',
        incidentDate: '2023-09-15' // Should trigger PAPPG v4.0
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Should auto-determine PAPPG version using our backend logic
      expect(response.body.user).toHaveProperty('pappgVersion');
      expect(response.body.user.pappgVersion).toContain('v4.0');
    });

    test('should auto-associate user with DR# and trigger workflow', async () => {
      // Create user first
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          password: 'SecurePassword123!',
          femaRegistration: 'FEMA-WORK'
        });

      const token = registerResponse.body.token;

      // Associate with specific disaster
      const incidentData = {
        drNumber: 'DR-4701-TX',
        incidentDate: '2023-09-15',
        state: 'TX',
        county: 'Harris'
      };

      const response = await request(app)
        .post('/api/fema/user-incident')
        .set('Authorization', `Bearer ${token}`)
        .send(incidentData)
        .expect(201);

      expect(response.body).toHaveProperty('workflow');
      expect(response.body.workflow).toHaveProperty('pappgVersion');
      expect(response.body.workflow).toHaveProperty('applicablePolicies');
      expect(response.body.workflow).toHaveProperty('complianceSteps');
    });
  });
});

// Custom Jest matchers for API testing
expect.extend({
  toBeValidJWT(received) {
    const jwtRegex = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/;
    const pass = jwtRegex.test(received);
    
    return {
      message: () => `expected ${received} ${pass ? 'not ' : ''}to be a valid JWT token`,
      pass,
    };
  }
});

module.exports = testDb; 